# 数据模型字段变更指南

本文档提供了在项目中添加、修改或删除数据模型字段的通用流程。遵循这些步骤可以确保字段变更在整个应用程序中的一致性和完整性。

## 使用方法

当需要对数据模型进行字段变更时，请提供以下信息：

1. 目标模块/表名（例如：`file`, `user`, `menu`等）
2. 变更类型（添加、修改或删除）
3. 字段详细信息：
   - 字段名称
   - 字段类型
   - 是否允许为空
   - 默认值（如有）
   - 描述信息
   - 其他特殊属性（如索引、唯一性等）
4. 前端表单中的展示方式（输入框、下拉菜单、日期选择器等）
5. 是否在列表中显示该字段

## 字段变更流程

### 1. 修改数据库模型

在 `app/models/{module_name}.py` 文件中进行相应的字段变更：

#### 添加字段
```python
# 在适当位置添加新字段
new_field = fields.CharField(max_length=255, null=True, description="字段描述")
```

#### 修改字段
```python
# 修改现有字段的属性
existing_field = fields.CharField(max_length=100, null=False, description="新的描述")
```

#### 删除字段
删除或注释掉相关字段定义。

### 2. 更新 Schema 定义

在 `app/schemas/{module_name}s.py` 文件中更新相应的 Pydantic 模型：

#### 添加字段
```python
# 在创建模型中添加字段
class ModuleCreate(BaseSchema):
    new_field: Optional[str] = Field(None, description="字段描述")

# 在更新模型中添加字段
class ModuleUpdate(BaseSchema):
    new_field: Optional[str] = Field(None, description="字段描述")

# 在输出模型中添加字段
class ModuleRead(BaseSchema):
    new_field: Optional[str] = Field(None, description="字段描述")
```

#### 修改字段
更新字段的类型、可选性或描述。

#### 删除字段
从所有相关 Schema 类中删除该字段。

### 3. 更新控制器

在 `app/controllers/{module_name}.py` 文件中，检查并更新任何直接引用该字段的方法：

#### 添加字段
如果控制器中有显式创建或更新对象的代码，确保包含新字段：

```python
obj_data = {
    # 其他字段...
    "new_field": data.new_field,
}
```

#### 修改字段
更新任何引用该字段的代码，确保类型和用法正确。

#### 删除字段
移除任何直接引用该字段的代码。

### 4. 更新 API 接口

在 `app/api/v1/{module_name}s/{module_name}s.py` 文件中，检查并更新任何直接引用该字段的代码：

#### 添加字段
如果有表单处理或特殊逻辑，确保包含新字段：

```python
@router.post("/", summary="创建新记录")
async def create_item(
    item_create: ModuleCreate,
    new_field: Optional[str] = Form(None, description="新字段"),
    # 其他参数...
):
    # 处理逻辑...
    data = item_create.model_dump()
    data["new_field"] = new_field
    # 其余代码...
```

#### 修改字段
更新任何引用该字段的代码，确保类型和用法正确。

#### 删除字段
移除任何直接引用该字段的代码。

### 5. 更新前端 API 接口

检查 `web/src/api/{module_name}.js` 文件，确保 API 调用正确传递新字段：

#### 添加/修改字段
通常不需要修改，因为大多数 API 函数会直接传递整个对象。

#### 删除字段
通常不需要修改，但可以从请求数据中移除该字段以减少传输数据量。

### 6. 更新前端视图组件

在 `web/src/views/{module_name}/index.vue` 或相关组件文件中：

#### 添加字段

1. 更新表单初始值：
```javascript
const initForm = {
  // 其他字段...
  new_field: '',
}
```

2. 添加表格列定义（如果需要在列表中显示）：
```javascript
const columns = [
  // 其他列...
  {
    title: '新字段',
    key: 'new_field',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
]
```

3. 添加表单项：
```html
<NFormItem
  label="新字段"
  path="new_field"
  :rule="{
    required: false,
    message: '请输入新字段',
    trigger: ['input', 'blur'],
  }"
>
  <NInput v-model:value="modalForm.new_field" placeholder="请输入新字段" />
</NFormItem>
```

#### 修改字段
更新表单项的标签、验证规则、输入组件等。

#### 删除字段
从表单和表格中移除相关代码。

### 7. 执行数据库迁移

#### 使用 Aerich（如果项目使用）
```bash
aerich migrate --name add_new_field_to_module
aerich upgrade
```

#### 手动 SQL 迁移
根据变更类型执行相应的 SQL 语句：

##### 添加字段
```sql
ALTER TABLE module_name ADD COLUMN new_field VARCHAR(255) NULL;
```

##### 修改字段
```sql
ALTER TABLE module_name ALTER COLUMN existing_field TYPE VARCHAR(100);
ALTER TABLE module_name ALTER COLUMN existing_field SET NOT NULL;
```

##### 删除字段
```sql
ALTER TABLE module_name DROP COLUMN field_to_delete;
```

## 测试计划

1. 后端测试：
   - 测试创建记录时是否可以设置新字段
   - 测试更新记录时是否可以修改字段
   - 测试获取记录列表时是否包含正确的字段

2. 前端测试：
   - 测试创建记录时是否可以输入新字段
   - 测试编辑记录时是否可以修改字段
   - 测试记录列表是否正确显示字段

## 字段类型参考

### Tortoise ORM 字段类型

| 字段类型 | 说明 | 对应 Pydantic 类型 |
|---------|------|------------------|
| fields.CharField | 字符串字段，需指定max_length | str |
| fields.TextField | 长文本字段 | str |
| fields.IntField | 整数字段 | int |
| fields.BigIntField | 大整数字段 | int |
| fields.FloatField | 浮点数字段 | float |
| fields.DecimalField | 精确小数字段 | Decimal |
| fields.BooleanField | 布尔字段 | bool |
| fields.DatetimeField | 日期时间字段 | datetime |
| fields.DateField | 日期字段 | date |
| fields.JSONField | JSON数据 | Dict 或自定义模型 |
| fields.ForeignKeyField | 外键关联 | int (关联ID) |
| fields.ManyToManyField | 多对多关联 | List[int] |
| fields.CharEnumField | 枚举字符串字段 | Enum |
| fields.IntEnumField | 枚举整数字段 | Enum |

### 字段常用参数

| 参数 | 说明 | 示例 |
|-----|------|------|
| max_length | 字符串最大长度 | max_length=50 |
| null | 是否允许为空 | null=True |
| default | 默认值 | default=0 |
| description | 字段描述 | description="用户名" |
| unique | 是否唯一 | unique=True |
| index | 是否创建索引 | index=True |

### 前端表单组件参考

| 数据类型 | 推荐组件 | 示例 |
|---------|---------|------|
| 字符串 | NInput | `<NInput v-model:value="form.field" />` |
| 长文本 | NInput type="textarea" | `<NInput v-model:value="form.field" type="textarea" />` |
| 整数/浮点数 | NInputNumber | `<NInputNumber v-model:value="form.field" />` |
| 布尔值 | NSwitch | `<NSwitch v-model:value="form.field" />` |
| 日期时间 | NDatePicker | `<NDatePicker v-model:value="form.field" type="datetime" />` |
| 日期 | NDatePicker | `<NDatePicker v-model:value="form.field" />` |
| 枚举/选项 | NSelect | `<NSelect v-model:value="form.field" :options="options" />` |
| 外键关联 | NSelect | `<NSelect v-model:value="form.field" :options="relatedOptions" />` |
| JSON/对象 | 自定义组件 | 根据具体需求设计 |

## 示例：添加描述字段到文件模块

### 1. 字段信息
- 模块：file
- 字段名：description
- 字段类型：TextField
- 允许为空：是
- 描述：文件或目录描述
- 前端组件：文本域
- 在列表中显示：是

### 2. 修改数据库模型
```python
# app/models/file.py
class File(BaseModel, TimestampMixin):
    # 现有字段...
    name = fields.CharField(max_length=255, description="当前名称 (文件或目录名)", index=True)
    description = fields.TextField(null=True, description="文件或目录描述")
    # 其他字段...
```

### 3. 更新 Schema
```python
# app/schemas/files.py
class FileCreate(BaseSchema):
    # 其他字段...
    description: Optional[str] = Field(None, description="文件或目录描述")

class FileUpdate(BaseSchema):
    # 其他字段...
    description: Optional[str] = Field(None, description="文件或目录描述")

class FileRead(BaseSchema):
    # 其他字段...
    description: Optional[str] = Field(None, description="文件或目录描述")
```

### 4. 更新前端组件
```html
<!-- web/src/views/file/index.vue -->
<NFormItem label="描述" path="description">
  <NInput v-model:value="modalForm.description" type="textarea" placeholder="请输入文件描述" />
</NFormItem>
```

```javascript
// 表格列定义
const columns = [
  // 其他列...
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
]
```

### 5. 数据库迁移
```sql
ALTER TABLE file ADD COLUMN description TEXT NULL;
```
# Bundle Explorer 设计方案总结

## 1. 需求概述

### 1.1 核心功能需求

- 实现可复用的 BundleExplorer 组件，根据传入的 bundle_type 进行对应渲染
- 左侧树形结构(BundleTree)展示 bundle 和 page 列表
- 右侧展示 Previewer 或 Editor 组件
- 支持不同 bundle_type 的预览和编辑功能
- 支持教育类试卷的特殊渲染需求，包括公式、图片等

### 1.2 交互需求

- 点击 bundle 节点展开/折叠该 bundle 下所有状态为 COMPLETED 的 page
- 单击 page 节点加载对应的 PagePreviewer 组件
- 双击 page 节点加载对应的 PageEditor 组件
- 对于不支持的 bundle_type 显示 404, 404.vue已经实现

### 1.3 特殊需求

- EducationPagePreviewer 需要渲染 LLM 解析出的复杂试题，包括嵌入图片
- 需要在预览时显示 page 图片，并根据 cv_result 在图片上绘制矩形框
- EducationPageEditor 需要支持对图片上矩形框的 CRUD 操作，并保存位置信息到 cv_result

## 2. 设计方案

### 2.1 目录结构

```
web/src/
├── components/
│   ├── bundle/
│   │   ├── BundleExplorer.vue       # 主组件
│   │   ├── BundleTree.vue           # 左侧树形结构组件
│   │   └── PageContainer.vue        # 右侧预览/编辑区域容器
│   ├── page/
│   │   ├── previewer/
│   │   │   ├── PagePreviewer.vue    # 预览器基础组件
│   │   │   ├── EducationPagePreviewer.vue  # 教育类预览器
│   │   │   └── [其他类型]PagePreviewer.vue  # 未来扩展
│   │   ├── editor/
│   │       ├── PageEditor.vue       # 编辑器基础组件
│   │       ├── EducationPageEditor.vue     # 教育类编辑器
│   │       └── [其他类型]PageEditor.vue     # 未来扩展
│   │   
├── views/
│   ├── bundle-explorer/
│   │   └── index.vue                # 文集浏览页面
```

### 2.2 组件设计

#### 2.2.1 BundleExplorer (主组件)

- **功能**：根据传入的 bundle_type 渲染对应的树形结构和预览/编辑区域
- **设计**：使用 NLayout 实现左右分栏布局
- **状态管理**：
  - selectedPage: 当前选中的 page 信息
  - currentMode: 当前模式 (preview/edit)

#### 2.2.2 BundleTree (左侧树形结构)

- **功能**：展示 bundle 和 page 列表，处理节点点击事件
- **设计**：使用 NTree 组件实现树形结构
- **数据加载**：
  - 初始加载对应 bundle_type 的所有 bundle
  - 展开 bundle 节点时加载其下的 page 列表
- **交互处理**：
  - 单击 page 节点触发预览
  - 双击 page 节点触发编辑

#### 2.2.3 PageContainer (右侧容器)

- **功能**：根据当前选中的 page 和模式动态加载对应的预览器或编辑器组件
- **设计**：使用动态组件和组件映射表实现不同类型组件的加载

#### 2.2.4 EducationPagePreviewer (教育类预览器)

- **功能**：
  - 显示页面图像
  - 在图像上绘制 cv_result 中的矩形框
  - 渲染 ocr_result 中的试题内容，包括公式和图片
- **技术选型**：
  - Fabric.js：用于图像上的矩形框绘制
  - MathJax-Vue3：用于渲染 LaTeX 数学公式
- **界面设计**：
  - 使用标签页分离原始图像、试题内容和原始数据视图
  - 使用卡片组件展示各个试题

#### 2.2.5 EducationPageEditor (教育类编辑器)

- **功能**：
  - 复用 EducationPagePreviewer 的显示功能
  - 支持对图像上矩形框的编辑操作
  - 提供 JSON 编辑器用于直接编辑 cv_result 和 ocr_result
  - 保存编辑结果到后端
- **交互设计**：
  - 工具栏提供添加区域、编辑 JSON、保存等操作
  - 支持拖拽调整矩形框位置和大小

### 2.3 技术选型

#### 2.3.1 UI 组件库

- **Naive UI**：提供基础布局和交互组件
  - NLayout：实现左右分栏布局
  - NTree：实现树形结构
  - NTabs：实现标签页切换
  - NCard：内容卡片展示
  - NModal：弹窗编辑

#### 2.3.2 特殊功能组件

- **Fabric.js**：Canvas 操作库
  - 用于图像上的矩形框绘制和编辑
  - 支持对象选择、移动、调整大小等交互功能
  - 提供事件系统用于捕获用户操作

- **MathJax-Vue3**：数学公式渲染
  - 用于渲染 LaTeX 数学公式
  - 支持行内公式和块级公式
  - 与 Vue3 良好集成

## 3. 实现细节

### 3.1 数据流

1. BundleExplorer 加载时，BundleTree 组件加载对应 bundle_type 的所有 bundle
2. 用户点击 bundle 节点时，加载该 bundle 下的所有 page
3. 用户单击 page 节点时，将该 page 信息传递给 PageContainer，并设置模式为 preview
4. 用户双击 page 节点时，将该 page 信息传递给 PageContainer，并设置模式为 edit
5. PageContainer 根据 bundle_type 和模式动态加载对应的预览器或编辑器组件
6. 预览器/编辑器组件加载 page 数据，并渲染内容

### 3.2 关键实现点

#### 3.2.1 动态组件加载

使用 Vue3 的 defineAsyncComponent 和组件映射表实现不同类型组件的动态加载：

```javascript
const previewerMap = {
  'education': markRaw(EducationPagePreviewer),
  // 未来可以添加更多类型
}

const editorMap = {
  'education': markRaw(EducationPageEditor),
  // 未来可以添加更多类型
}

const currentComponent = computed(() => {
  if (!props.page) return null
  const componentMap = props.mode === 'preview' ? previewerMap : editorMap
  return componentMap[props.bundleType] || NotFound
})
```

#### 3.2.2 Canvas 绘制与交互

使用 Fabric.js 实现图像上矩形框的绘制和编辑：

```javascript
// 初始化 Canvas
fabricCanvas.value = new fabric.Canvas(canvasRef.value, {
  selection: props.editMode,
  interactive: props.editMode
})

// 加载图像
fabric.Image.fromURL(pageData.value.image_url, (img) => {
  // 设置图像为背景
  fabricCanvas.value.add(img)
  img.sendToBack()
  
  // 渲染矩形框
  renderCVRegions()
})

// 处理矩形修改事件
rect.on('modified', (e) => {
  // 更新 CV 结果中的位置信息
  updateCVResult(e.target)
})
```

#### 3.2.3 公式渲染

使用 MathJax-Vue3 渲染 LaTeX 数学公式：

```html
<MathJax :formula="question.processedContent" />
```

## 4. 潜在风险与解决方案

### 4.1 性能风险

**风险**：
- 大量 bundle 和 page 数据可能导致树形结构渲染缓慢
- Canvas 上大量矩形框可能影响交互性能
- 复杂公式渲染可能导致页面卡顿

**解决方案**：
- 实现树形结构的懒加载和虚拟滚动
- 优化 Canvas 渲染，仅在可视区域绘制矩形框
- 使用 Vue 的 Suspense 和异步组件延迟加载
- 考虑使用 Web Worker 处理复杂计算

### 4.2 兼容性风险

**风险**：
- Fabric.js 和 MathJax 在某些浏览器上可能存在兼容性问题
- 不同设备上 Canvas 性能差异较大

**解决方案**：
- 明确支持的浏览器版本范围
- 添加降级方案，在低性能设备上使用简化版渲染
- 进行充分的跨浏览器测试

### 4.3 数据处理风险

**风险**：
- CV 结果和 OCR 结果数据结构可能变化
- 大型 JSON 数据编辑容易出错

**解决方案**：
- 实现数据适配层，处理不同版本的数据结构
- 添加 JSON 验证机制，防止无效数据提交
- 提供撤销/重做功能，允许用户恢复误操作

### 4.4 用户体验风险

**风险**：
- 复杂的编辑操作可能导致用户学习成本高
- 双击/单击的交互模式可能不够直观

**解决方案**：
- 添加操作引导和提示
- 考虑使用更明确的按钮或上下文菜单替代双击操作
- 实现自动保存功能，防止数据丢失

## 5. 扩展性考虑

- **新增 bundle_type 支持**：只需添加对应的预览器和编辑器组件，并更新组件映射表
- **新增编辑功能**：可以在编辑器组件中添加新的工具和操作
- **多语言支持**：使用 i18n 实现界面多语言
- **主题切换**：利用 Naive UI 的主题系统实现暗色/亮色模式切换

## 6. 总结

本设计方案提供了一个灵活、可扩展的 Bundle Explorer 实现，能够满足不同类型文档的浏览和编辑需求。通过组件化设计和动态加载，实现了代码的高度复用和良好的扩展性。特别针对教育类试卷的特殊需求，选择了合适的技术组件实现复杂内容的渲染和编辑功能。

方案充分考虑了性能优化、用户体验和潜在风险，提供了相应的解决方案和优化建议。随着系统的发展，可以轻松扩展支持更多类型的文档和更丰富的编辑功能。
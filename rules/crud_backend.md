
# 后端 CRUD 实现指南

本文档总结了后端 CRUD 功能实现的步骤和最佳实践。
文档内容以菜单管理模块为例，对于其他新功能的CRUD使用相同的流程。只需要替换必要的文件名和模型定义即可。

## 实现步骤

### 1. 创建模型定义

在 `app/models` 目录下创建或更新相应的数据模型。

```python
# 示例: app/models/menu.py
from tortoise import fields
from app.models.base import TimestampMixin, BaseDBModel

class Menu(BaseDBModel, TimestampMixin):
    name = fields.CharField(max_length=50, description="菜单名称")
    path = fields.CharField(max_length=200, description="菜单路径")
    component = fields.CharField(max_length=200, null=True, description="组件路径")
    redirect = fields.CharField(max_length=200, null=True, description="重定向路径")
    meta_title = fields.CharField(max_length=50, description="标题")
    meta_icon = fields.CharField(max_length=50, null=True, description="图标")
    meta_hidden = fields.BooleanField(default=False, description="是否隐藏")
    meta_sort = fields.IntField(default=0, description="排序")
    parent_id = fields.IntField(null=True, description="父级菜单ID")
    status = fields.BooleanField(default=True, description="状态")

    class Meta:
        table = "menu"
        table_description = "菜单表"
```

### 2. 创建 Schema 定义

在 `app/schemas` 目录下创建相应的 Pydantic 模型，用于请求和响应的数据验证。
因为有些字段是不允许编辑(比如hash），有的字段是自动生成或者数据库自动更新(比如更新日期)，
有的字段是不希望用户看见(比如密码)，有的字段是只读(比如创建日期)。
请根据你的经验，从用户角度设计好创建、更新以及读取这些场景下需要给用户看见的字段，从而更好的设计schemas。

```python
# 示例: app/schemas/menus.py
from typing import Optional, List
from pydantic import BaseModel, Field

class MenuBase(BaseModel):
    name: str = Field(..., description="菜单名称")
    path: str = Field(..., description="菜单路径")
    component: Optional[str] = Field(None, description="组件路径")
    redirect: Optional[str] = Field(None, description="重定向路径")
    meta_title: str = Field(..., description="标题")
    meta_icon: Optional[str] = Field(None, description="图标")
    meta_hidden: bool = Field(False, description="是否隐藏")
    meta_sort: int = Field(0, description="排序")
    parent_id: Optional[int] = Field(None, description="父级菜单ID")
    status: bool = Field(True, description="状态")

class MenuCreate(MenuBase):
    pass

class MenuUpdate(MenuBase):
    name: Optional[str] = Field(None, description="菜单名称")
    path: Optional[str] = Field(None, description="菜单路径")
    meta_title: Optional[str] = Field(None, description="标题")

class MenuRead(MenuBase):
    id: int
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True
```

### 3. 创建 Controller

在 `app/controllers` 目录下创建控制器，实现业务逻辑。
如果要获取当前用户的id可以通过环境对象获取: `user_id = CTX_USER_ID.get()`

```python
# 示例: app/controllers/menu.py
from typing import List, Optional, Dict, Any
from app.controllers.base import CRUDBase
from app.models.menu import Menu

class MenuCRUD(CRUDBase):
    model = Menu

    async def get_menus(self, **kwargs) -> List[Dict[str, Any]]:
        menus = await self.get_multi(**kwargs)
        return menus

    async def create_menu(self, **kwargs) -> Dict[str, Any]:
        menu = await self.create(**kwargs)
        return menu

    async def update_menu(self, id: int, **kwargs) -> Optional[Dict[str, Any]]:
        menu = await self.update(id=id, **kwargs)
        return menu

    async def delete_menu(self, id: int) -> bool:
        result = await self.delete(id=id)
        return result

menu_crud = MenuCRUD()
```

### 4. 创建 API 路由

在 `app/api/v1` 目录下创建相应的 API 路由文件和目录。

```python
# 示例: app/api/v1/menus/menus.py
from typing import List
from fastapi import APIRouter, Depends, Query
from app.schemas.base import Success, Fail
from app.schemas.menus import MenuCreate, MenuUpdate, MenuOut
from app.controllers.menu import menu_crud
from app.core.security import get_current_user
from app.models.user import User

router = APIRouter()

@router.get("", response_model=Success[List[MenuOut]])
async def get_menus(
    page: int = Query(1, description="页码"),
    limit: int = Query(10, description="每页数量"),
    name: str = Query(None, description="菜单名称"),
    current_user: User = Depends(get_current_user)
):
    kwargs = {"page": page, "limit": limit}
    if name:
        kwargs["name__contains"] = name
    
    menus = await menu_crud.get_menus(**kwargs)
    return Success(data=menus)

@router.post("", response_model=Success[MenuOut])
async def create_menu(
    menu: MenuCreate,
    current_user: User = Depends(get_current_user)
):
    menu_data = await menu_crud.create_menu(**menu.model_dump())
    return Success(data=menu_data)

@router.put("/{menu_id}", response_model=Success[MenuOut])
async def update_menu(
    menu_id: int,
    menu: MenuUpdate,
    current_user: User = Depends(get_current_user)
):
    menu_data = await menu_crud.update_menu(id=menu_id, **menu.model_dump(exclude_unset=True))
    return Success(data=menu_data)

@router.delete("/{menu_id}", response_model=Success)
async def delete_menu(
    menu_id: int,
    current_user: User = Depends(get_current_user)
):
    result = await menu_crud.delete_menu(id=menu_id)
    if result:
        return Success(msg="删除成功")
    return Fail(msg="删除失败")
```

```python
# 示例: app/api/v1/menus/__init__.py
from fastapi import APIRouter
from app.api.v1.menus.menus import router as menus_router

router = APIRouter()
router.include_router(menus_router, tags=["菜单管理"])
```

### 5. 注册 API 路由

在 `app/api/v1/__init__.py` 中注册新创建的路由。

```python
# 示例: app/api/v1/__init__.py (部分代码)
from fastapi import APIRouter
from app.api.v1.users import router as users_router
from app.api.v1.roles import router as roles_router
from app.api.v1.menus import router as menus_router
# 导入其他路由...

router = APIRouter()
router.include_router(users_router, prefix="/users")
router.include_router(roles_router, prefix="/roles")
router.include_router(menus_router, prefix="/menus")
# 注册其他路由...
```

## 数据库字段类型参考

### 常用字段类型

| Tortoise ORM 字段类型 | 说明 | Pydantic 类型 |
|---------------------|------|--------------|
| fields.CharField | 字符串字段，需指定max_length | str |
| fields.TextField | 长文本字段 | str |
| fields.IntField | 整数字段 | int |
| fields.FloatField | 浮点数字段 | float |
| fields.BooleanField | 布尔字段 | bool |
| fields.DatetimeField | 日期时间字段 | datetime |
| fields.ForeignKeyField | 外键关联 | int (关联ID) |
| fields.ManyToManyField | 多对多关联 | List[int] |
| fields.JSONField | JSON数据 | Dict 或自定义模型 |

### 字段常用参数

| 参数 | 说明 | 示例 |
|-----|------|------|
| max_length | 字符串最大长度 | max_length=50 |
| null | 是否允许为空 | null=True |
| default | 默认值 | default=0 |
| description | 字段描述 | description="用户名" |
| unique | 是否唯一 | unique=True |
| index | 是否创建索引 | index=True |

## 权限控制

为确保API安全，应在路由中添加适当的权限控制：

```python
from app.core.permission import permission_required

@router.post("", response_model=Success[MenuOut])
async def create_menu(
    menu: MenuCreate,
    current_user: User = Depends(get_current_user)
):
    @permission_required("menu:create")
    async def _create_menu():
        menu_data = await menu_crud.create_menu(**menu.model_dump())
        return Success(data=menu_data)
    
    return await _create_menu()
```

## 错误处理

使用 try-except 块处理可能的异常，并返回标准化的错误响应：

```python
@router.post("", response_model=Success[MenuOut])
async def create_menu(
    menu: MenuCreate,
    current_user: User = Depends(get_current_user)
):
    try:
        menu_data = await menu_crud.create_menu(**menu.model_dump())
        return Success(data=menu_data)
    except Exception as e:
        logger.error(f"创建菜单失败: {str(e)}")
        return Fail(msg="创建菜单失败")
```


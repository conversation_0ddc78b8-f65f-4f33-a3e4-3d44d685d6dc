# 后端技术栈指南

本文档用于指导后端开发人员在实现功能时选择合适的技术和架构。为了保持项目的一致性和可维护性，所有新功能的实现都应遵循本指南中的技术选型和实现规范。

## 技术选型

### 核心框架
- 使用 Python 3.11+ 和 FastAPI 框架
- 使用 Tortoise ORM 进行数据库操作
- 使用 Pydantic 进行数据验证和序列化
- 使用 JWT 进行身份验证和授权
- 使用 RBAC 模型进行权限控制
- 使用异步编程模式

### AI技术栈
- 使用 LangChain 作为AI应用开发框架
- 使用 LangGraph 构建AI代理工作流
- 使用 Jinja2 进行提示词模板管理
- 支持多种LLM模型（基础模型、推理模型、视觉模型）
- 集成多种搜索引擎（DuckDuckGo、Brave Search、Arxiv）
- 支持Python代码执行工具

## 代码实现Guideline

### 通用原则
- **模块化设计**：遵循项目的目录结构，将代码组织到适当的模块中
- **控制器模式**：业务逻辑放在 controllers 目录下，与 API 路由分离
- **数据验证**：使用 Pydantic schemas 进行请求和响应的数据验证
- **错误处理**：统一使用异常处理机制，返回标准化的错误响应
- **权限控制**：在router进行统一的认证授权依赖注入， 不需要在每个API endpoint进行。
- **日志记录**：使用项目的 logger 进行日志记录
- **数据库操作**：使用 CRUDBase 基类进行标准 CRUD 操作。 如果有复杂逻辑，可以再在controller(CRUDBase的子类)中重载对应方法或重新封装新的方法。
- **异步编程**：尽可能使用异步函数和异步 ORM 操作

### AI功能开发原则
- **模型类型**：使用 LLMType 枚举定义模型类型（basic/reasoning/vision）
- **代理创建**：使用 create_agent 工厂函数创建AI代理，支持工具和提示词模板
- **提示词管理**：使用 Jinja2 模板系统管理提示词，支持变量替换
- **工具开发**：使用装饰器模式为AI工具添加日志记录功能

## 核心组件使用参考

### 使用 CRUDBase 进行数据库操作

```python
from app.controllers.base import CRUDBase
from app.models.file import File

class FileCRUD(CRUDBase):
    model = File

    async def create_file(self, **kwargs):
        obj = await self.create(**kwargs)
        return obj

    async def get_files(self, **kwargs):
        return await self.get_multi(**kwargs)
```

### 使用 Pydantic 进行数据验证

```python
from pydantic import BaseModel, Field
from typing import Optional

class FileCreate(BaseModel):
    name: str = Field(..., description="文件名称")
    path: str = Field(..., description="文件路径")
    size: int = Field(..., description="文件大小")
    type: Optional[str] = Field(None, description="文件类型")
```

### 使用标准响应模型

```python
from app.schemas.base import Success, Fail

@router.post("/files/", response_model=Success[FileOut])
async def create_file(file: FileCreate):
    try:
        obj = await file_crud.create_file(**file.model_dump())
        return Success(data=obj)
    except Exception as e:
        logger.error(f"创建文件失败: {str(e)}")
        return Fail(msg="创建文件失败")
```

### 使用 JWT 认证和权限控制

```python
from app.core.security import get_current_user
from app.core.permission import permission_required

@router.get("/files/{file_id}", response_model=Success[FileOut])
async def get_file(
    file_id: int,
    current_user: User = Depends(get_current_user)
):
    @permission_required("file:read")
    async def _get_file():
        obj = await file_crud.get(id=file_id)
        return Success(data=obj)

    return await _get_file()
```

### 使用事务确保数据一致性

```python
from tortoise.transactions import in_transaction

async def update_with_related(file_id: int, data: dict):
    async with in_transaction():
        # 更新文件
        file = await file_crud.update(id=file_id, **data)
        # 更新关联数据
        await related_crud.update_for_file(file_id=file_id, **data)
        return file
```

### 使用异步任务处理耗时操作 [暂时没有引入Celery， 忽略这个规则]

```python
from app.core.celery_app import celery_app

@celery_app.task
def process_file(file_id: int):
    # 处理文件的耗时操作
    pass

# 在API中调用
@router.post("/files/{file_id}/process")
async def start_processing(file_id: int):
    process_file.delay(file_id)
    return Success(msg="处理任务已启动")
```

## AI组件使用参考

### 使用LLM进行对话

```python
from app.ai.llms import get_llm_by_type

async def chat_with_llm(message: str, llm_type: str = "basic"):
    try:
        llm = get_llm_by_type(llm_type)
        response = await llm.ainvoke(message)
        return Success(data={"response": response.content})
    except Exception as e:
        logger.error(f"LLM调用失败: {str(e)}")
        return Fail(msg="AI服务暂时不可用")
```

### 创建和使用AI代理

```python
from app.ai.agents import create_agent
from app.ai.tools import python_repl_tool, get_web_search_tool
from app.ai.prompts import get_prompt_template

async def create_analysis_agent():
    """创建数据分析代理"""
    tools = [python_repl_tool, get_web_search_tool()]
    prompt_template = get_prompt_template("default")

    agent = create_agent(
        agent_name="data_analyst",
        agent_type="reasoning",  # 使用推理模型
        tools=tools,
        prompt_template=prompt_template
    )
    return agent

async def run_agent_task(agent, user_input: str):
    """运行代理任务"""
    try:
        result = await agent.ainvoke({"messages": [{"role": "user", "content": user_input}]})
        return Success(data={"result": result})
    except Exception as e:
        logger.error(f"代理执行失败: {str(e)}")
        return Fail(msg="代理执行失败")
```

### 使用提示词模板

```python
from app.ai.prompts import apply_prompt_template, get_prompt_template

# 获取模板内容
template_content = get_prompt_template("user_question")

# 在代理中应用模板（自动处理变量替换）
def create_custom_agent():
    return create_agent(
        agent_name="assistant",
        agent_type="basic",
        tools=[],
        prompt_template="user_question"  # 使用预定义模板
    )
```

### 使用AI工具

```python
from app.ai.tools import python_repl_tool, get_web_search_tool

async def execute_python_code(code: str):
    """执行Python代码"""
    try:
        result = python_repl_tool.invoke(code)
        return Success(data={"result": result})
    except Exception as e:
        logger.error(f"代码执行失败: {str(e)}")
        return Fail(msg="代码执行失败")

async def search_web(query: str):
    """网络搜索"""
    try:
        search_tool = get_web_search_tool()
        result = search_tool.invoke(query)
        return Success(data={"result": result})
    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        return Fail(msg="搜索失败")
```

### 自定义AI工具

```python
from langchain_core.tools import tool
from app.ai.tools.decorators import log_io
from app.log import logger

@tool
@log_io
def custom_analysis_tool(data: str) -> str:
    """自定义数据分析工具"""
    try:
        # 实现自定义分析逻辑
        result = f"分析结果: {data}"
        return result
    except Exception as e:
        logger.error(f"分析工具执行失败: {str(e)}")
        return f"分析失败: {str(e)}"

# 在代理中使用自定义工具
def create_agent_with_custom_tools():
    tools = [custom_analysis_tool, python_repl_tool]
    return create_agent(
        agent_name="custom_analyst",
        agent_type="reasoning",
        tools=tools,
        prompt_template="default"
    )
```
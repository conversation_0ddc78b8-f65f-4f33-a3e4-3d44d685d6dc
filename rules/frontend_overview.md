# 前端架构概览

## 1. 主要技术架构

前端项目基于以下技术栈构建：

- **框架**: Vue 3 - 使用 Composition API 和 `<script setup>` 语法
- **UI组件库**: Naive UI - 提供丰富的UI组件和样式
- **状态管理**: Pinia - 用于管理应用状态
- **构建工具**: Vite - 提供快速的开发体验和构建优化
- **路由**: Vue Router - 处理页面路由
- **HTTP请求**: Axios - 封装的request工具进行API调用
- **国际化**: Vue I18n - 支持多语言
- **图标系统**: 使用 TheIcon 组件显示图标，支持多种图标库
- **权限控制**: 使用 v-permission 指令控制功能访问
- **样式**: UnoCSS + SCSS - 原子化CSS和预处理器
- **工具库**: VueUse - 提供常用的组合式函数

项目采用了模块化的架构设计，遵循Vue 3的最佳实践，包括：
- 使用Composition API进行状态和逻辑管理
- 组件样式作用域隔离
- 使用keyed v-for
- 避免v-if与v-for一起使用
- 基础组件使用特定前缀

## 2. CRUD公共组件设计

项目实现了一套完整的CRUD操作通用组件体系，主要包括：

### 2.1 核心Composable

**useCRUD**: 一个可复用的组合式函数，封装了常见的CRUD操作逻辑：
```javascript
const {
  modalVisible,    // 控制模态框显示
  modalTitle,      // 模态框标题
  modalLoading,    // 模态框加载状态
  handleSave,      // 保存处理函数
  modalForm,       // 表单数据对象
  modalFormRef,    // 表单引用
  handleEdit,      // 编辑处理函数
  handleDelete,    // 删除处理函数
  handleAdd,       // 添加处理函数
} = useCRUD({
  name: '实体名称',
  initForm: { /* 初始表单数据 */ },
  doCreate: api.create,
  doUpdate: api.update,
  doDelete: api.delete,
  refresh: () => $table.value?.handleSearch(),
})
```

### 2.2 通用组件

- **CommonPage**: 通用页面布局组件，提供标准的页面结构，包括标题、操作区和内容区
- **CrudTable**: 数据表格组件，支持搜索、分页、排序等功能，自动处理数据加载和刷新
- **CrudModal**: 用于创建和编辑数据的模态框组件，提供标准的表单布局和操作按钮
- **QueryBar/QueryBarItem**: 查询条件栏组件，用于构建搜索表单
- **TheIcon**: 图标组件，支持多种图标库

### 2.3 权限控制

使用`v-permission`指令控制UI元素的显示：

```html
<n-button v-permission="'resource:action'" @click="handleAction">
  操作按钮
</n-button>
```

### 2.4 CRUD实现流程

实现一个新的CRUD功能模块通常遵循以下步骤：

1. 创建API接口函数（在`web/src/api`目录下）
2. 创建视图组件（在`web/src/views`目录下）
3. 配置路由（添加到路由配置中）
4. 实现列表展示（使用CrudTable）
5. 实现添加/编辑功能（使用CrudModal和useCRUD）
6. 实现删除功能（使用useCRUD的handleDelete）
7. 添加权限控制（使用v-permission指令）

## 3. 目录结构

以下是`web/src`目录的树形结构及各文件的主要功能：

```
web/src/
├── api/                    # API接口定义目录
│   ├── file.js             # 文件管理相关API
│   └── index.js            # 核心API和通用API定义
├── assets/                 # 静态资源目录
│   ├── images/             # 图片资源
│   ├── js/                 # JavaScript资源
│   └── svg/                # SVG图标资源
├── components/             # 全局组件目录
│   ├── common/             # 通用组件
│   │   ├── AppFooter.vue           # 应用页脚组件
│   │   ├── AppProvider.vue         # 应用提供者组件，提供全局配置
│   │   ├── LoadingEmptyWrapper.vue # 加载和空状态包装器
│   │   └── ScrollX.vue             # 水平滚动组件
│   ├── icon/               # 图标相关组件
│   │   ├── CustomIcon.vue          # 自定义图标组件
│   │   ├── IconPicker.vue          # 图标选择器组件
│   │   ├── SvgIcon.vue             # SVG图标组件
│   │   └── TheIcon.vue             # 统一图标组件
│   ├── page/               # 页面相关组件
│   │   ├── AppPage.vue             # 应用页面组件
│   │   └── CommonPage.vue          # 通用页面组件
│   ├── query-bar/          # 查询栏组件
│   │   ├── QueryBar.vue            # 查询栏容器组件
│   │   └── QueryBarItem.vue        # 查询栏项目组件
│   └── table/              # 表格相关组件
│       ├── CrudModal.vue           # CRUD模态框组件
│       └── CrudTable.vue           # CRUD表格组件
├── composables/            # 组合式函数目录
│   ├── index.js            # 组合式函数导出
│   └── useCRUD.js          # CRUD操作组合式函数
├── directives/             # 自定义指令目录
│   ├── index.js            # 指令注册
│   └── permission.js       # 权限控制指令
├── layout/                 # 布局目录
│   ├── components/         # 布局组件
│   │   ├── AppMain.vue             # 主内容区组件
│   │   ├── header/                 # 头部组件
│   │   ├── sidebar/                # 侧边栏组件
│   │   └── tags/                   # 标签页组件
│   └── index.vue           # 主布局组件
├── router/                 # 路由目录
│   ├── guard/              # 路由守卫
│   │   ├── auth-guard.js           # 认证守卫
│   │   ├── index.js                # 守卫注册
│   │   ├── page-loading-guard.js   # 页面加载守卫
│   │   └── page-title-guard.js     # 页面标题守卫
│   ├── routes/             # 路由定义
│   │   └── index.js                # 基础路由和动态路由
│   └── index.js            # 路由配置和初始化
├── store/                  # 状态管理目录
│   ├── modules/            # 状态模块
│   │   ├── app/                    # 应用状态
│   │   ├── permission/             # 权限状态
│   │   ├── tags/                   # 标签页状态
│   │   ├── user/                   # 用户状态
│   │   └── index.js                # 模块导出
│   └── index.js            # 状态管理初始化
├── styles/                 # 样式文件目录
│   ├── global.scss         # 全局样式
│   └── reset.css           # 重置样式
├── utils/                  # 工具函数目录
│   ├── auth/               # 认证相关工具
│   ├── common/             # 通用工具函数
│   ├── http/               # HTTP请求工具
│   ├── storage/            # 存储工具
│   └── index.js            # 工具函数导出
├── views/                  # 视图组件目录
│   ├── error-page/         # 错误页面
│   ├── file/               # 文件管理页面
│   ├── login/              # 登录页面
│   ├── profile/            # 个人资料页面
│   ├── system/             # 系统管理页面
│   │   ├── api/                    # API管理
│   │   ├── auditlog/               # 审计日志
│   │   ├── dept/                   # 部门管理
│   │   ├── menu/                   # 菜单管理
│   │   ├── role/                   # 角色管理
│   │   └── user/                   # 用户管理
│   ├── top-menu/           # 顶部菜单页面
│   └── workbench/          # 工作台页面
├── App.vue                 # 应用根组件
└── main.js                 # 应用入口文件
```

## 4. 开发规范

### 4.1 组件设计

- 组件名称使用多词命名（如TodoItem而非Item）
- 基础组件使用特定前缀（如Base, App, V）
- 复杂功能拆分为较小的可复用组件
- 使用Composition API和`<script setup>`语法组织代码

### 4.2 样式规范

- 组件样式使用scoped属性或其他作用域隔离方式
- 对于大型项目，推荐使用基于类的策略（如BEM）而非scoped属性

### 4.3 权限控制

- 使用v-permission指令控制UI元素的显示
- 根据用户权限动态生成路由和菜单

### 4.4 错误处理

- 统一处理API请求错误
- 提供友好的用户错误提示

### 4.5 响应式设计

- 确保在不同屏幕尺寸下的良好体验
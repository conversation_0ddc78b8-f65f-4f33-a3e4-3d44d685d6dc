# 前端 CRUD 实现指南

本文档总结了前端 CRUD 功能实现的步骤和最佳实践。 
文档内容以文件管理模块为例， 对于其他新功能的CRUD使用相同的流程。只需要替换必要的文件名和API接口即可。
对于增加、编辑对话框可修改的字段，请参考对应后端schemas的定义，一般对应 XXXCreate, XXXUpdate pydantic model中定义。

## 实现步骤

### 1. 创建 API 接口

检查后端相关模块API的路由定义， 在 `web/src/api` 目录下添加相关的 API 接口函数。
添加的api要和对应的后端API endpoint实现相匹配。

```javascript
// 示例: web/src/api/file.js
import { request } from '@/utils/request'

export default {
  getFiles: (params) => request.get('/api/v1/files', { params }),
  uploadFile: (data) => request.post('/api/v1/files', data),
  updateFile: (id, data) => request.put(`/api/v1/files/${id}`, data),
  deleteFile: (id) => request.delete(`/api/v1/files/${id}`),
  // 其他必要的API接口
}
```

### 2. 创建视图组件

创建主视图组件和必要的子组件。

```
web/src/views/file/index.vue         # 主视图组件
web/src/views/file/components/       # 子组件目录（如果需要）
```
### 3. 创建路由

本系统使用后端动态加载路由，不需要创建静态路由。


## 本次需要实现的功能
- page的CRUD功能

### feat-1. 列表展示

显示所有的字段，除了：
- create_time
- custom_metadata
点击 bundle_id, file_id， 跳转到对应的 bundle 或者 file 的详情页面

### feat-2. 增加操作


### feat-3. 修改操作


### feat-4. 删除操作






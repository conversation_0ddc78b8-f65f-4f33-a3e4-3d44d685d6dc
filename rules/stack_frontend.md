# 前端技术栈指南

本文档用于指导前端开发人员在实现功能时选择合适的技术和架构。为了保持项目的一致性和可维护性，所有新功能的实现都应遵循本指南中的技术选型和实现规范。

## 技术选型

- 使用 Vue 3 的 Composition API 和 `<script setup>` 语法
- 使用 Naive UI 组件库
- 使用 TheIcon 组件显示图标
- 使用 useCRUD composable 处理 CRUD 操作
- 使用 CommonPage、CrudTable、CrudModal 等通用组件
- 使用 v-permission 指令控制权限

## 代码实现Guideline

- **组件拆分**：参考系统其他部分的组件实现，尽量复用相似功能的组件以及代码组织结构。否则将复杂功能拆分为较小的，可复用的组件
- **状态管理**：使用 Composition API 管理组件状态
- **错误处理**：统一处理 API 请求错误
- **权限控制**：使用 `v-permission` 指令控制功能访问
- **响应式设计**：确保在不同屏幕尺寸下的良好体验
- **代码风格**：保持与项目其他部分一致的代码风格和架构模式
- **UI 界面**: 对于前端UI显示的字段及其属性(比如显示、是否必选等等)，请参考后端对应模块的API以及schema定义。
- **注意事项**: 不要在  NTooltip、NPopover 等包装组件上直接使用 v-permission 将指令应用到实际的交互元素（如  NButton）上

## 通用组件使用参考

### 使用 useCRUD Composable
利用 `useCRUD` 处理常见的 CRUD 操作：

```javascript
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: '文件',
  initForm: { /* 初始表单数据 */ },
  doCreate: api.uploadFile,
  doUpdate: api.updateFile,
  doDelete: api.deleteFile,
  refresh: () => $table.value?.handleSearch(),
})
```

### 权限控制
使用 `v-permission` 指令控制按钮的显示：

```html
<n-button v-permission="'file:add'" @click="handleAdd">
  新增
</n-button>
```

### 文件上传组件
使用 Naive UI 的 `n-upload` 组件实现文件上传：

```html
<n-upload
  :action="uploadUrl"
  :headers="headers"
  :data="uploadParams"
  @finish="handleUploadFinish"
>
  <n-button>上传文件</n-button>
</n-upload>
```
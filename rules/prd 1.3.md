# 产品需求文档 (PRD) - 试卷编辑器与智能解析平台

**修订历史**
| 版本 | 日期 | 描述 | 作者 |
|------|------|------|------|
| 1.0 | 2023-10-27 | 初始版本，定义核心功能 | [原作者] |
| 1.1 | 2023-10-28 | 细化文件管理、试卷集概念、页面级异步解析、更新数据结构 | [AI助手] |
| 1.2 | 2023-10-28 | 更新技术选型建议和部署方案 | [AI助手] |

## 1. 产品概述

### 1.1 背景与目标

本项目旨在开发一个基于Web的试卷智能解析与编辑平台，为K12教育领域的教师和内容编辑人员提供高效的试卷数字化解决方案。系统支持用户上传并管理多种格式的原始文件（如Word、PDF、扫描件、图片），这些文件可以组合成一个“试卷集”。通过图像处理技术和AI技术，系统能将试卷集中的各个文件按页面进行异步解析，转换为结构化的题目和插图数据。平台提供专业的编辑工具对解析结果进行人工校准和完善，处理完成后保存在数据库形成题库，以便外部系统使用。

**主要目标：**
-   实现对用户上传的多种格式原始文件进行统一管理。
-   支持将多个原始文件逻辑组合成一个“试卷集”进行处理。
-   实现试卷集内各文件内容的高效、自动化的页面级数字化转换。
-   提供专业、易用的试卷编辑环境，对自动化解析的题目内容进行人工校准。
-   确保内容结构化存储与管理，并跟踪解析状态。
-   降低教育工作者处理试卷的时间成本。
-   确保原始文件、页面、题目、插图之间的可追溯性。

### 1.2 核心价值

1.  **效率提升**：将手动录入试卷的时间减少80%以上，通过异步处理优化用户等待时间。
2.  **准确性**：智能识别准确率目标达95%以上。
3.  **专业性**：支持LaTeX公式、图片等混排编辑。
4.  **可追溯性**：原始文件、页面、数字化题目和插图之间维持清晰的映射关系。
5.  **结构化**：标准化的JSON数据结构便于系统间集成和数据分析。
6.  **模块化管理**：对原始文件、页面、题目、插图进行独立而关联的管理。

### 1.3 目标用户

-   K12阶段各学科教师
-   教辅材料编辑人员
-   在线教育平台内容创建者
-   教育机构数字化转型负责人

### 1.4 关键指标

| 指标 | 目标值 | 说明 |
|------|-------|------|
| 识别准确率 | >95% | 对于标准印刷体试卷页面 |
| 页面解析速度 | <60秒/页 | 单页内容的完整解析时间（异步后台） |
| 编辑效率 | >80% | 相比传统手动录入方法的时间节省 |
| 用户学习曲线 | <30分钟 | 新用户掌握核心功能所需时间 |
| 系统可用性 | >99.9% | |

## 2. 功能规格

### 2.1 原始文件管理 (Original File Management)

#### 2.1.1 文件上传
-   **支持格式**：Word(.doc/.docx)、PDF、图片(.jpg/.png/.bmp)。
-   **上传方式**：拖拽上传、文件选择器。
-   **批量上传**：支持一次上传多个文件。

#### 2.1.2 文件列表与操作
-   **文件展示**：列表形式展示用户上传的所有原始文件，包含文件名、格式、大小、上传时间、关联试卷集等信息。
-   **文件预览**：支持在线预览常见格式文件（如PDF、图片）。
-   **文件删除**：用户可以删除不再需要的原始文件（若文件已关联到试卷集并解析，需有提示或限制）。
-   **文件搜索与筛选**：按文件名、格式、上传日期等条件进行搜索和筛选。

#### 2.1.3 文件元数据编辑
-   **可编辑信息**：文件名（用户自定义别名）、来源描述、备注等。
-   **系统记录信息**：文件ID、原始文件名、文件类型、大小、上传者、上传时间、MD5值。

### 2.2 试卷集管理 (Exam Collection Management)

#### 2.2.1 试卷集创建与构成
-   **创建试卷集**：用户可以创建一个新的试卷集，并为其命名、选择科目、年级等元数据。
-   **关联文件**：用户可以从已上传的原始文件列表中选择一个或多个文件添加到一个试卷集中。一个原始文件理论上可以被多个试卷集引用（需明确业务逻辑，一般为一对多或多对多关系，建议初期为一对多：一个文件属于一个试卷集，或不属于任何试卷集）。
-   **动态调整内容**：允许向已创建的试卷集中动态添加或移除原始文件（图片或其他格式）。

#### 2.2.2 试卷集操作
-   **列表展示**：展示用户创建的所有试卷集，包含名称、包含文件数、创建时间、解析状态等。
-   **编辑元数据**：修改试卷集的标题、科目、年级等信息。
-   **删除试卷集**：删除试卷集及其与原始文件的关联（不一定删除原始文件本身，除非用户选择）。
-   **查看详情**：查看试卷集包含的原始文件列表及各文件的解析状态。

### 2.3 智能解析与处理 (Intelligent Parsing and Processing)

#### 2.3.1 文件到页面转换 (Asynchronous Task)
-   **触发方式**：当原始文件被添加到试卷集后，或用户手动触发对试卷集（内含文件）的解析。
-   **处理逻辑**：
    -   Word/PDF文档：按页拆分为独立的页面图像。
    -   图片文件：本身视为单页图像。
-   **数据产出**：为每个页面生成一个“页面实体(Page)”记录，包含：
    -   页面ID、所属原始文件ID、在原文件中的页码。
    -   页面图像的存储路径（对象存储）。
    -   页面尺寸等元数据。
-   **状态跟踪**：数据库记录每个文件的页面拆分任务状态（待处理、处理中、完成、失败）。

#### 2.3.2 页面内容解析 (Asynchronous Task)
-   **触发方式**：页面转换完成后，自动或手动触发对单个或多个页面的内容解析。
-   **处理逻辑**：
    -   **图像预处理**：对页面图像进行去噪、增强、版面分析等。
    -   **内容提取 (AI OCR)**：识别页面中的文本、公式。
    -   **结构识别**：自动区分题干、选项、答案、解析等组成部分，并初步划分题目区域。
    -   **插图提取**：识别并提取页面中的插图，保存为独立的“插图实体(Illustration)”，记录其在页面上的位置信息。
    -   **题目切分**：根据结构识别结果，将页面内容切分为独立的“题目实体(Question)”。
    -   **自动解题 (可选)**：使用大语言模型对识别出的题目尝试推理出参考答案和解题分析。
-   **数据产出**：
    -   为每个识别的题目生成“题目实体(Question)”记录，关联到来源页面ID。
    -   为每个提取的插图生成“插图实体(Illustration)”记录，关联到来源页面ID，并可选关联到对应题目ID。
-   **状态跟踪**：数据库记录每个页面的内容解析任务状态（待处理、处理中、完成、失败）。

#### 2.3.3 解析结果管理
-   **任务监控**：提供界面展示文件拆分、页面解析的整体进度和各任务的详细状态。
-   **源文件关联**：确保题目、插图能追溯到其来源页面及原始文件。
-   **快速校对预览**：
    -   提供原始页面图像与解析出的题目、插图列表的对照预览。
    -   高亮显示题目/插图在原页面的区域。
-   **编辑衔接**：一键将选定试卷集下已解析的全部题目导入编辑器进行后续处理。

### 2.4 试题编辑器 (Question Editor)

#### 2.4.1 试卷上下文加载
-   **加载试卷集**：从指定试卷集加载所有已解析并确认的题目。
-   **题目组织**：支持按原文件及页码顺序显示题目，或按大题结构（如选择题、填空题）组织。允许用户调整题目顺序，自动更新编号。
-   **分数分配**：设置各题目/大题分值。
-   **元数据编辑**：在编辑视图中，可修改试卷集的整体信息（标题、科目等）。

#### 2.4.2 题目导航与操作
-   **列表视图**：左侧栏紧凑显示试卷集内所有题目列表，便于快速浏览和定位。
-   **当前编辑标识**：高亮显示正在主编辑区编辑的题目。
-   **快速跳转**：点击题目列表项，直接切换到对应题目的编辑状态。
-   **添加题目**：手动选择题型，创建新题目。
-   **删除题目**：移除现有题目（逻辑删除或物理删除，需确认机制）。
-   **复制题目**：快速复制现有题目及其内容。
-   **移动题目**：通过拖拽调整题目在试卷集内的顺序。

#### 2.4.3 基于题型的动态内容编辑
-   **核心机制**：根据当前编辑题目的题型，动态加载相应的专用编辑组件。
-   **组件切换**：更改题型时，自动切换并适配编辑界面，尽可能保留已有内容。
-   **富文本编辑器核心**：
    -   文本格式化：加粗、斜体、下划线、上下标、列表、字体、颜色等。
    -   公式编辑：支持LaTeX行内公式和块级公式，提供可视化编辑器或直接输入LaTeX源码。
    -   图片插入/管理：支持从已提取的插图库中选择，或上传新图片到题目中。
    -   实时渲染：所见即所得(WYSIWYG)预览模式。
-   **题型特定编辑功能**：
    -   选择题：选项增删改、选项内容编辑（支持富文本）、正确答案标记（单选/多选）。
    -   判断题：提供正/误选择控件。
    -   填空题：在题干中标记空位，并对应编辑每个空的答案。
    -   解答题/大题：支持分点作答、步骤化答案编辑、评分点设置。

#### 2.4.4 插图管理 (编辑器内)
-   **插图显示**：在题目编辑区域正确显示与该题目关联的插图。
-   **关联操作**：将页面解析出的插图关联到具体题目，或取消关联。
-   **插图编辑**：提供简单的编辑功能，如裁剪、旋转（高级功能）。
-   **位置调整**：调整插图在题目内容中的相对位置。
-   **替换/删除**：替换或删除题目中的插图。

#### 2.4.5 保存与导出
-   **实时/自动保存**：编辑过程中的内容变更应有自动保存机制，防止数据丢失。
-   **手动保存**：用户确认完成编辑后，将整个试卷集的结构化数据（包含所有题目、插图信息）保存到数据库。
-   **导出格式**：支持导出为标准化的JSON数据结构。未来可考虑导出为Word、PDF等。

## 3. 用户体验规格

### 3.1 界面布局

#### 3.1.1 整体布局
-   **导航栏**：顶部或左侧固定导航，区分“文件管理”、“试卷集管理”、“试题编辑”等主要模块。
-   **文件管理模块**：文件上传区、文件列表区、文件详情/元数据编辑区。
-   **试卷集管理模块**：试卷集列表区、试卷集创建/编辑区、关联文件管理区、解析任务监控区。
-   **试题编辑模块**：经典三栏布局：
    1.  **左栏 (题目导航区)**：试卷集内题目列表，支持筛选和快速跳转。
    2.  **中栏 (主编辑区)**：当前选中题目的动态编辑组件。
    3.  **右栏 (属性/工具区)**：当前题目/组件的属性设置、试卷集元数据、插图库等。

![主编辑区布局示意图] (示意图占位符，实际应有图)

#### 3.1.2 主编辑区设计
-   **动态组件容器**：清晰展示当前题型并加载对应编辑界面。
-   **工具栏布局**：富文本编辑工具栏置于编辑区域上方或随选区浮动，常用功能优先，分组清晰。
-   **空间利用**：确保内容编辑区域最大化，辅助工具区紧凑高效。

### 3.2 交互设计

#### 3.2.1 核心解析与编辑流程
1.  **文件上传**：用户进入“文件管理”，上传一个或多个原始试卷文件。
2.  **创建试卷集**：用户进入“试卷集管理”，创建新试卷集，并从已上传文件中选择并关联至此试卷集。
3.  **启动解析**：用户对试卷集或其中特定文件发起解析。系统后台异步执行“文件到页面转换”和“页面内容解析”。
4.  **监控状态**：用户可在试卷集详情或任务中心查看解析进度和各页面状态。
5.  **结果预览与校对**：解析完成后，用户预览页面与提取的题目/插图，进行初步核对。
6.  **导入编辑器**：用户选择将整个试卷集的解析结果导入“试题编辑器”。
7.  **精细编辑**：在编辑器中，用户逐题校对、修改、完善内容，调整顺序，管理插图，设置分值等。
8.  **保存**：完成编辑后，保存整个试卷集的结构化数据。

### 3.3 反馈机制

-   **操作确认**：对删除文件、删除试卷集、删除题目等重要操作提供二次确认。
-   **状态提示**：清晰显示异步任务（文件上传、页面拆分、内容解析）的进度（百分比、队列位置）和结果（成功、失败及原因）。
-   **加载提示**：长时间操作（如加载大型试卷集）时提供加载动画或进度条。
-   **错误处理**：发生错误时，提供用户友好的错误信息和可能的解决方案或重试选项。
-   **实时反馈**：编辑操作（如文本格式化）应即时在编辑器中反映出来。

### 3.4 用户旅程

**场景1：从零开始处理一份新试卷**
1.  登录系统，进入“文件管理”模块。
2.  上传试卷的原始文件（如一个PDF和几张补充图片）。
3.  进入“试卷集管理”模块，点击“创建试卷集”。
4.  填写试卷集名称（如“高一上期末数学模拟卷”）、科目等信息。
5.  从文件列表中勾选刚上传的PDF和图片，将其添加到此试卷集。
6.  点击“开始解析”按钮，系统开始后台处理。
7.  用户可在列表中看到试卷集的解析状态，或点进详情查看各文件及页面的解析进度。
8.  解析完成后，状态更新为“待校对”或“已完成”。用户点击“进入编辑”或从导航进入“试题编辑器”并加载此试卷集。
9.  在编辑器中，逐题检查AI识别结果，修改错误，调整格式，管理插图。
10. 添加/删除题目，调整题目顺序，设置分数。
11. 完成编辑后，点击“保存试卷集”。

**场景2：编辑已存在的试卷集或手动创建题目**
1.  登录系统，进入“试卷集管理”模块。
2.  找到目标试卷集，点击“编辑”或“查看详情”后进入编辑模式。
3.  或创建一个空的试卷集，直接进入“试题编辑器”。
4.  在编辑器中，选择“添加题目”，选择题型。
5.  使用富文本编辑器和题型特定组件手动录入题目内容。
6.  管理插图（可从本地上传新图，或复用系统中已有的）。
7.  编辑完成后，保存试卷集。

## 4. 技术规格

### 4.1 性能要求

| 要素 | 指标 | 说明 |
|------|------|------|
| 文件上传速度 | 取决于用户网络，服务端接收能力 >50MB/s | |
| 页面拆分速度 | <5秒/页 (PDF/Word转图片) | 后台异步任务 |
| 页面内容解析速度 | <60秒/页 | 后台异步任务，标准印刷体 |
| 编辑器加载(大型试卷集) | <5秒 | 100道题规模的试卷集 |
| 组件切换(题型变更) | <500ms | 编辑组件的加载渲染时间 |
| 公式渲染(复杂公式) | <200ms | 单个复杂LaTeX公式的实时渲染 |
| API响应时间 (用户交互) | <200ms (P95) | 不含耗时计算的常规API |

### 4.2 兼容性

-   **浏览器支持**：Chrome 90+、Firefox 90+、Edge 90+、Safari 15+。
-   **设备适配**：优先桌面端，确保在1366×768以上分辨率正常显示和操作。
-   **网络要求**：稳定网络连接以保证文件上传和数据同步。编辑器部分操作可考虑离线支持和本地缓存。

### 4.3 安全与数据完整性

-   **数据存储**：
    -   原始文件：存储于对象存储服务（如S3, MinIO）。
    -   结构化数据（元数据、题目、插图信息）：存储于关系型数据库或NoSQL数据库。
-   **数据备份与恢复**：数据库定期备份，有恢复机制。
-   **版本追踪**：对试卷集和题目的重要编辑操作记录历史版本，支持有限的回滚（高级功能）。
-   **权限控制**：用户数据隔离，仅能访问和操作自己的文件和试卷集。可扩展支持组织和共享。
-   **数据验证**：前后端对用户输入和API数据进行校验，确保数据结构的完整性和一致性。
-   **防丢失机制**：编辑器内容定期自动保存到草稿。

### 4.4 异步任务处理
-   **任务类型**：文件到页面转换、页面内容解析等耗时操作均设计为后台异步任务。
-   **技术选型**：使用消息队列（如RabbitMQ, Kafka, Redis Streams）+ 后台 Worker 服务架构。
-   **状态管理**：数据库中为每个原始文件、页面记录其当前的解析状态（如：待处理、处理中、已完成、失败、失败原因）。
-   **用户通知**：当耗时较长的异步任务完成或失败时，应有适当的通知机制（如站内信、小红点提示）。

### 4.5 技术选型建议

#### 4.5.1 前端技术栈
-   **框架**: Vue 3
-   **语言**: TypeScript
-   **UI组件库**: Naive UI
-   **状态管理**: Pinia
-   **构建工具**: Vite

#### 4.5.2 后端技术栈
-   **API框架**: FastAPI (Python)
-   **任务框架**: Celery + Redis
-   **数据库 (关系型)**: SQLite (开发环境), PostgreSQL (生产环境)
-   **缓存**: Redis
-   **消息队列**: Redis (利用其 Streams 或 Pub/Sub 功能)
-   **ORM**: Tortoise ORM
-   **对象存储**: MinIO (S3兼容)
-   **LLM模型服务**: 兼容OpenAI python SDK。

#### 4.5.3 核心组件与服务技术选型
-   **AI解析技术**:
    -   **核心方案**: OCR (如 Tesseract OCR, PaddleOCR, 或商业OCR API) + 版面分析模型 (如 LayoutLM, PaddleOCR版面分析) + LLM (用于结构化信息提取、校正、自动解题)。
    -   **视觉大模型 (VLLM)**：如具备相应能力，可作为端到端或关键步骤的解决方案。
    -   **评估标准**: 中文识别准确率（特别是数理化公式）、版面理解能力、速度、部署成本、可定制性。
-   **富文本编辑器**:
    -   **推荐选项**:
        *   TipTap (Vue3): 基于ProseMirror，高度可扩展，对Vue3友好。
        *   CKEditor 5: 功能成熟全面，有官方Vue组件。
    -   **核心考量**: LaTeX支持（MathJax/KaTeX集成）、图片处理、自定义组件嵌入能力、性能、社区活跃度、Vue3集成便利性。

#### 4.5.4 部署方案
-   **部署方式**: 容器化部署 (Docker)
-   **编排工具 (开发/测试环境)**: Docker Compose
-   **编排工具 (生产环境)**: Kubernetes (K8s) 或类似容器编排平台 (可选，根据规模和运维能力)
-   **服务器操作系统**: Ubuntu Server (推荐LTS版本，如22.04 LTS)
-   **服务器资源建议 (初步)**:
    *   **vCPU**: 根据并发用户数和解析任务负载评估，建议至少 4-8 vCPU 起步。
    *   **vRAM**: 根据应用内存占用、数据库、缓存及AI模型需求评估，建议至少 16-32GB vRAM 起步。AI模型服务（尤其是LLM推理）可能需要更高配置或专用GPU资源。
    *   **存储**: 充足的SSD磁盘空间用于操作系统、应用、数据库数据、日志及MinIO对象存储（若本地部署）。
-   **持续集成/持续部署 (CI/CD)**: 推荐引入，如 GitLab CI/CD, Jenkins, GitHub Actions，实现自动化构建、测试和部署。

## 5. 数据结构 (核心实体示例)

### 5.1 原始文件 (File)
```python
class File(BaseModel, TimestampMixin):
    """
    Model to store metadata for files and directories stored in object storage (MinIO).
    Represents both files and directories.
    """
    # 1. Delete user will not delete the files:
    #    - User field should be nullable if user can be deleted.
    #    - on_delete=fields.SET_NULL will set user_id to NULL when user is deleted.
    user = fields.ForeignKeyField(
        "models.User",
        related_name="files",
        description="关联用户 (文件所有者)",
        index=True,
        null=True,  # Allow user_id to be NULL if user is deleted
        on_delete=fields.SET_NULL # If user is deleted, set user_id to NULL on these files
    )

    # Self-referencing foreign key for directory structure
    # 1. Delete folder (parent) will delete all files under it:
    #    - on_delete=fields.CASCADE will delete children if parent is deleted.
    #    - This applies to hard deletes. For soft deletes, app logic handles recursion.
    parent: fields.ForeignKeyNullableRelation["File"] = fields.ForeignKeyField(
        "models.File",
        related_name="children",
        null=True,               # Root items have no parent
        description="父级ID (指向父目录)",
        index=True,
        on_delete=fields.CASCADE # If a parent directory is (hard) deleted, cascade delete children
    )

    # --- Core Attributes ---
    is_directory = fields.BooleanField(default=False, description="是否为目录", index=True)
    name = fields.CharField(max_length=255, description="当前名称 (文件或目录名)", index=True)
    original_name = fields.CharField(max_length=255, null=True, description="原始文件名 (上传时)")
    description = fields.TextField(null=True, description="文件或目录描述")

    # --- File Specific Attributes (Nullable for directories) ---
    mime_type = fields.CharField(max_length=100, null=True, description="MIME类型", index=True)
    file_extension = fields.CharField(max_length=20, null=True, description="文件后缀名", index=True)
    size_bytes = fields.BigIntField(null=True, description="文件大小 (字节)")
    content_hash = fields.CharField(max_length=128, null=True, description="文件内容哈希", index=True)
    hash_algorithm = fields.CharEnumField(HashAlgorithm, max_length=10, null=True, description="哈希算法")
    page_count = fields.IntField(null=True, description="页数 (例如PDF)")

    # --- Storage Specific Attributes ---
    bucket_name = fields.CharField(max_length=63, description="MinIO Bucket名称")
    object_key = fields.CharField(max_length=1024, unique=True, null=True, description="MinIO对象存储路径/Key (仅文件)", index=True)

    # --- Status and Custom Data ---
    status_processing = fields.CharEnumField(
        ProcessingStatus,
        max_length=20,
        null=True,
        description="后台处理状态 (页面拆分、预览生成等)",
        index=True
    )
    custom_metadata = fields.JSONField(null=True, description="用户自定义元数据 (JSON)")

    # --- Soft Delete ---
    deleted_at = fields.DatetimeField(null=True, index=True, description="软删除时间标记 (NULL表示未删除)")

    # --- Relationships defined by related_name ---
    children: fields.ReverseRelation["File"]
```

### 5.2 页面 (Page)
```Python
class Page(BaseModel, TimestampMixin):
    """
    Model to store page data extracted from files.
    A page belongs to a single bundle and is associated with a file.
    """
    # --- Foreign Key Relationships ---
    bundle = fields.ForeignKeyField(
        "models.Bundle",
        related_name="pages",
        description="关联的Bundle",
        index=True,
        on_delete=fields.CASCADE  # If bundle is deleted, cascade delete pages
    )
    
    file = fields.ForeignKeyField(
        "models.File",
        related_name="pages",
        description="关联的文件",
        index=True,
        on_delete=fields.SET_NULL  # If file is deleted, cascade delete pages
    )

    # --- Core Attributes ---
    page_number = fields.IntField(
        description="bundle中的页码 (从1开始, 会根据文件上传的顺序重新编页)",
        index=True
    )
    
    # --- Processing Status and Results ---
    process_status = fields.CharEnumField(
        ProcessingStatus,
        max_length=20,
        description="内容解析状态",
        default=ProcessingStatus.NEW,
        index=True
    )
    process_result = fields.JSONField(
        null=True,
        description="解析结果的JSON表示，包含OCR文本、结构信息等"
    )
    process_task_id = fields.CharField(
        max_length=64,
        null=True,
        description="关联的异步任务ID，用于追踪处理进度",
        index=True
    )
    # --- Analysis Results ---
    ocr_result = fields.JSONField(
        null=True,
        description="原始OCR分析结果"
    )
    cv_result = fields.JSONField(
        null=True,
        description="原始版面图像分析结果"
    )
    
    # --- Image Attributes ---
    image_url = fields.CharField(
        max_length=1024,
        null=True,
        description="页面转换后的图像文件在对象存储的URL"
    )
    image_width = fields.IntField(
        null=True,
        description="图像宽度 (像素)"
    )
    image_height = fields.IntField(
        null=True,
        description="图像高度 (像素)"
    )
    image_ppi = fields.IntField(
        null=True,
        description="图像分辨率 (点/英寸)"
    )
    # --- Custom Data ---
    custom_metadata = fields.JSONField(
        null=True,
        description="用户可编辑的元数据"
    )
```

### 5.3 文件集 (Bundle)
```python
class Bundle(BaseModel, TimestampMixin):
    """
    Model to store logical collections of files (Bundles).
    """

    name = fields.CharField(max_length=255, description="Bundle名称", index=True)
    description = fields.TextField(null=True, description="Bundle描述")
    bundle_type = fields.CharEnumField(
        BundleType,
        max_length=30,
        description="Bundle类型 (决定处理方式)",
        index=True,
        default=BundleType.GENERAL
    )
    status_processing = fields.CharEnumField(
        ProcessingStatus,
        max_length=20,
        description="Bundle处理状态",
        default=ProcessingStatus.NEW
    )
    custom_metadata = fields.JSONField(null=True, description="Bundle自定义元数据 (JSON)")
    processing_result = fields.JSONField(null=True, description="Bundle处理/解析后的结果 (JSON)")
    deleted_at = fields.DatetimeField(null=True, index=True, description="软删除时间标记 (NULL表示未删除)")

    # --- 多对多关系定义 ---
    # 这个 'files' 字段定义了 Bundle 与 File 之间的多对多关系。
    files: fields.ManyToManyRelation["File"] = fields.ManyToManyField(
        "models.File",                 # 关联的模型名称 (字符串形式)
        related_name="bundles",        # 在 File 模型中反向访问此关系的名称
        description="与此Bundle关联的文件集合"
    )
    # file_associations: fields.ReverseRelation["BundleFile"] # 如果需要直接访问中间表记录

    user = fields.ForeignKeyField(
        "models.User",  # 指向 User 模型
        related_name="bundles",
        description="关联用户 (Bundle所有者)",
        null=True,
        on_delete=fields.SET_NULL
    )
```


### 5.4 插图 (Illustration)
```json
{
  "illustration_id": "uuid", // 主键
  "page_id": "uuid", // 外键，关联到Page，表示插图来源页面
  "question_id": "uuid_nullable", // (可选) 外键，若此插图已关联到特定题目
  "image_url": "string", // 插图自身（可能是裁剪后）在对象存储的URL
  "bounding_box_on_page": { // 在来源页面上的位置和尺寸 (单位：像素或百分比)
    "x": "number",
    "y": "number",
    "width": "number",
    "height": "number"
  },
  "caption_text": "string_nullable", // AI识别的图注或用户编辑的图注
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### 5.5 题目 (Question)
```json
{
  "question_id": "uuid", // 主键
  "collection_id": "uuid", // 外键，关联到ExamCollection，表示题目所属试卷集
  "page_id": "uuid_nullable", // 外键，关联到Page，表示题目主要内容来源页面 (若手动创建则可为空)
  "order_in_collection": "integer", // 题目在试卷集中的顺序号
  "question_type": "enum(SINGLE_CHOICE, MULTI_CHOICE, TRUE_FALSE, FILL_IN_BLANK, ESSAY, COMPREHENSIVE)",
  "stem_html": "text_long", // 题干内容 (HTML格式，包含文本、公式、图片引用等)
  "options_json": "jsonb_nullable", // 对于选择题：[{"id":"A", "content_html":"text"}, ...]
  "answer_json": "jsonb", // 答案，结构随题型变化：
                          // 选择题："A" or ["A", "B"]
                          // 判断题：true/false
                          // 填空题：["ans1", "ans2_html"]
                          // 解答题：{"steps": [{"content_html":"step1", "score": 5}, ...], "final_answer_html": "..."}
  "analysis_html": "text_long_nullable", // 解析内容 (HTML格式)
  "score": "decimal_nullable", // 分值
  "difficulty": "enum_nullable(EASY, MEDIUM, HARD)",
  "knowledge_tags": ["string"], // 知识点标签
  "illustration_ids_associated": ["uuid"], // 数组，关联的Illustration ID列表
  "bounding_box_on_page": { // (可选) 题目在来源页面上的大致包围框
    "x": "number", "y": "number", "width": "number", "height": "number"
  },
  "source_details": { // 记录来源的更多细节
      "original_question_number": "string_nullable" // AI识别的原题号
  },
  "is_manually_added": "boolean", // 是否为手动添加的题目
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

## 6. 未来规划

### 6.1 下一阶段考虑

-   **批量处理增强**：支持对多个试卷集同时发起解析或执行其他批量操作。
-   **AI辅助校对**：基于AI模型对识别内容进行语法、逻辑错误检查，并给出修正建议。
-   **智能组卷与推题**：基于题库内容和用户需求（知识点、难度）智能推荐题目或辅助组卷。
-   **导出格式扩展**：支持将编辑完成的试卷集导出为Word、PDF（带格式）、教学平台兼容格式等。
-   **多用户协作**：支持多个用户对同一试卷集进行协同编辑（需引入版本控制和冲突解决机制）。

### 6.2 长期规划

-   **手写体识别优化**：提升对手写试卷和手写答案的识别准确率。
-   **版本控制系统**：为试卷集和题目提供完善的修订历史、版本比较和回滚功能。
-   **教育数据分析**：对试卷集的题目难度分布、知识点覆盖率、学生作答数据（如能接入）进行统计分析，提供教学洞察。
-   **自适应学习集成**：与自适应学习系统对接，根据学生画像和学习路径动态调整出题。
-   **公式编辑器增强**：提供更强大的可视化公式编辑能力，或集成手写公式识别输入。

## 7. 附录

### 7.1 术语表

| 术语 | 定义 |
|------|------|
| RTE | 富文本编辑器(Rich Text Editor) |
| OCR | 光学字符识别(Optical Character Recognition) |
| VLLM | 视觉大型语言模型(Visual Large Language Model) |
| LLM | 大型语言模型(Large Language Model) |
| WYSIWYG | 所见即所得(What You See Is What You Get) |
| LaTeX | 专业排版系统，特别适用于数学公式 |
| 试卷集 (Exam Collection) | 用户定义的一个逻辑单元，包含一个或多个原始文件，是解析和编辑的基本对象。 |
| 原始文件 (Original File) | 用户上传的未经处理的初始文件，如PDF、Word文档或图片。 |
| 页面 (Page) | 从原始文件（特别是多页文档）中拆分出来的单个页面图像及其元数据，是内容解析的最小单元。 |
| 插图 (Illustration) | 从页面中识别并提取出来的图片元素。 |
| 题目 (Question) | 从页面中识别并结构化处理得到的一个完整问题单元。 |
| 异步任务 | 在后台执行的操作，不阻塞用户前台交互，如文件转换、内容解析。 |

### 7.2 参考资料

-   [LaTeX数学符号参考手册]
-   [常见题型结构标准规范调研]
-   [主流OCR及版面分析技术性能对比]
-   [对象存储服务选型（如MinIO, AWS S3）]
-   [消息队列技术选型（如RabbitMQ, Kafka, Redis Streams）]
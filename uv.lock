# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o uv.lock
aerich==0.8.1
    # via vue-fastapi-admin (pyproject.toml)
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.6
    # via langchain-community
aiosignal==1.3.2
    # via aiohttp
aiosqlite==0.20.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   tortoise-orm
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   pydantic
anyio==4.8.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   asyncclick
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
argon2-cffi==23.1.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   minio
argon2-cffi-bindings==21.2.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   argon2-cffi
asyncclick==8.1.8
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   aerich
attrs==25.3.0
    # via aiohttp
billiard==4.2.1
    # via celery
black==24.10.0
    # via vue-fastapi-admin (pyproject.toml)
celery==5.5.3
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   flower
certifi==2024.12.14
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   httpcore
    #   httpx
    #   minio
    #   requests
cffi==1.17.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   argon2-cffi-bindings
charset-normalizer==3.4.2
    # via requests
click==8.1.8
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   black
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   rich-toolkit
    #   typer
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
dataclasses-json==0.6.7
    # via langchain-community
dictdiffer==0.9.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   aerich
distro==1.9.0
    # via openai
dnspython==2.7.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   email-validator
email-validator==2.2.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
fastapi==0.111.0
    # via vue-fastapi-admin (pyproject.toml)
fastapi-cli==0.0.7
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
flower==2.0.1
    # via vue-fastapi-admin (pyproject.toml)
frozenlist==1.6.0
    # via
    #   aiohttp
    #   aiosignal
greenlet==3.2.2
    # via sqlalchemy
h11==0.14.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   httpx
httptools==0.6.4
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   uvicorn
httpx==0.28.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
    #   langgraph-sdk
    #   langsmith
    #   openai
httpx-sse==0.4.0
    # via langchain-community
humanize==4.12.3
    # via flower
idna==3.10
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
iso8601==2.1.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   tortoise-orm
isort==5.13.2
    # via vue-fastapi-admin (pyproject.toml)
jinja2==3.1.5
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
jiter==0.10.0
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
kombu==5.5.4
    # via celery
langchain==0.3.25
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   langchain-community
langchain-community==0.3.24
    # via langchain-experimental
langchain-core==0.3.63
    # via
    #   langchain
    #   langchain-community
    #   langchain-experimental
    #   langchain-openai
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-experimental==0.3.4
    # via vue-fastapi-admin (pyproject.toml)
langchain-openai==0.3.18
    # via vue-fastapi-admin (pyproject.toml)
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.4.7
    # via vue-fastapi-admin (pyproject.toml)
langgraph-checkpoint==2.0.26
    # via
    #   langgraph
    #   langgraph-prebuilt
langgraph-prebuilt==0.2.2
    # via langgraph
langgraph-sdk==0.1.70
    # via langgraph
langsmith==0.3.43
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
loguru==0.7.3
    # via vue-fastapi-admin (pyproject.toml)
markdown-it-py==3.0.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   rich
markupsafe==3.0.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   jinja2
marshmallow==3.26.1
    # via dataclasses-json
mdurl==0.1.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   markdown-it-py
minio==7.2.15
    # via vue-fastapi-admin (pyproject.toml)
multidict==6.4.4
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.0.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   black
    #   typing-inspect
numpy==2.2.6
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   langchain-community
    #   opencv-python-headless
openai==1.82.1
    # via langchain-openai
opencv-python-headless==4.11.0.86
    # via vue-fastapi-admin (pyproject.toml)
orjson==3.10.14
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
packaging==24.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   black
    #   kombu
    #   langchain-core
    #   langsmith
    #   marshmallow
    #   pyproject-toml
passlib==1.7.4
    # via vue-fastapi-admin (pyproject.toml)
pathspec==0.12.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   black
platformdirs==4.3.6
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   black
prometheus-client==0.22.0
    # via flower
prompt-toolkit==3.0.51
    # via click-repl
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
pycparser==2.22
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   cffi
pycryptodome==3.23.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   minio
pydantic==2.10.5
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   aerich
    #   fastapi
    #   langchain
    #   langchain-core
    #   langgraph
    #   langsmith
    #   openai
    #   pydantic-settings
    #   pyproject-toml
pydantic-core==2.27.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   pydantic
pydantic-settings==2.7.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   langchain-community
pygments==2.19.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   rich
pyjwt==2.10.1
    # via vue-fastapi-admin (pyproject.toml)
pymupdf==1.26.0
    # via vue-fastapi-admin (pyproject.toml)
pypika-tortoise==0.3.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   tortoise-orm
pyproject-toml==0.1.0
    # via vue-fastapi-admin (pyproject.toml)
python-dateutil==2.9.0.post0
    # via celery
python-dotenv==1.0.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.20
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
pytz==2024.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   flower
    #   tortoise-orm
pyyaml==6.0.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   langchain
    #   langchain-community
    #   langchain-core
    #   uvicorn
redis==6.2.0
    # via vue-fastapi-admin (pyproject.toml)
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   langchain
    #   langchain-community
    #   langsmith
    #   requests-toolbelt
    #   tiktoken
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.4
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   rich-toolkit
    #   typer
rich-toolkit==0.13.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi-cli
ruff==0.9.1
    # via vue-fastapi-admin (pyproject.toml)
setuptools==75.8.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   pyproject-toml
shellingham==1.5.4
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   typer
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   anyio
    #   openai
sqlalchemy==2.0.41
    # via
    #   langchain
    #   langchain-community
starlette==0.37.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
tenacity==9.1.2
    # via
    #   langchain-community
    #   langchain-core
tiktoken==0.9.0
    # via langchain-openai
tornado==6.5.1
    # via flower
tortoise-orm==0.23.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   aerich
tqdm==4.67.1
    # via openai
typer==0.15.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi-cli
typing-extensions==4.12.2
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   aiosqlite
    #   anyio
    #   fastapi
    #   langchain-core
    #   minio
    #   openai
    #   pydantic
    #   pydantic-core
    #   rich-toolkit
    #   sqlalchemy
    #   typer
    #   typing-inspect
typing-inspect==0.9.0
    # via dataclasses-json
tzdata==2025.2
    # via kombu
ujson==5.10.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
urllib3==2.4.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   minio
    #   requests
uvicorn==0.34.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   fastapi
    #   fastapi-cli
uvloop==0.21.0
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   uvicorn
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
watchfiles==1.0.4
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   uvicorn
wcwidth==0.2.13
    # via prompt-toolkit
websockets==14.1
    # via
    #   vue-fastapi-admin (pyproject.toml)
    #   uvicorn
wheel==0.45.1
    # via pyproject-toml
xxhash==3.5.0
    # via langgraph
yarl==1.20.0
    # via aiohttp
zstandard==0.23.0
    # via langsmith

<script setup>
import { ref, reactive, computed, onMounted, nextTick, h, withDirectives } from 'vue'
import { NButton, NTag, NIcon, NBreadcrumb, NBreadcrumbItem, NInput, NFormItem, NForm, NPopconfirm, NModal, NText, NProgress, useMessage } from 'naive-ui'
import { resolveDirective } from 'vue'

import CommonPage from '@/components/page/CommonPage.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'

import { formatDate, formatSize, renderIcon, getToken } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import fileApi from '@/api/file'

defineOptions({ name: '文件管理' })

// Setup message service
const message = useMessage()

// Table reference
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// Table params
const tableParams = computed(() => ({
  parent_id: currentDirId.value
}))

// Current directory navigation path
// navigationPath is an array of file objects, where the last element is the current directory
// Empty array means we're at the root directory
const navigationPath = ref([])
const currentDirId = computed(() => {
  if (!navigationPath.value || navigationPath.value.length === 0) {
    return null // Root directory
  }
  const lastItem = navigationPath.value[navigationPath.value.length - 1]
  return lastItem && lastItem.id ? lastItem.id : null
})

// File upload
const fileInput = ref(null)
const uploadForm = ref(null)
const selectedFile = ref(null)
const uploadProgress = ref(0)
const uploadMessage = ref('')
const uploadSuccess = ref(false)
const isUploading = ref(false)

// 上传需要的参数
const uploadData = computed(() => {
  // Ensure parent_id is always a valid value (null for root directory)
  const parentId = currentDirId.value !== undefined ? currentDirId.value : null
  return {
    parent_id: parentId,
    // Add user_id if needed
    // user_id: useUserStore().userId
  }
})
const showUploadModal = ref(false)

// Form initialization
const initForm = {
  name: '',
  parent_id: null,
  description: '',
  custom_metadata: null
}

// CRUD operations
// Custom save handler for directory creation
function customCreateDirectory(formData) {
  // Make sure original_name is set to the name field
  const data = { ...formData }

  // Ensure original_name is set
  if (data.name && !data.original_name) {
    data.original_name = data.name
  }

  // Ensure parent_id is correctly set
  if (data.parent_id === undefined || data.parent_id === null) {
    data.parent_id = currentDirId.value
  }

  console.log('Creating directory with data:', data)
  return fileApi.createDirectory(data)
}

const {
  modalVisible,
  modalTitle,
  modalLoading,
  modalAction,
  handleAdd,
  handleDelete,
  handleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '文件',
  initForm,
  doCreate: customCreateDirectory,
  doUpdate: fileApi.updateFile,
  doDelete: fileApi.deleteFile,
  refresh: () => $table.value?.handleSearch(),
})

// Table columns
const columns = [
  {
    title: '名称',
    key: 'name',
    width: 250,
    ellipsis: { tooltip: true },
    render(row) {
      // For directories, add a click handler to navigate into the directory
      if (row.is_directory) {
        return h(
          'div',
          {
            class: 'flex items-center cursor-pointer',
            onClick: () => navigateToDirectory(row),
          },
          [
            h(NIcon, { size: 20, class: 'mr-2' }, { default: () => h(TheIcon, { icon: 'mdi:folder' }) }),
            h('span', {}, row.name)
          ]
        )
      } else {
        // For files, show file icon based on mime_type
        return h(
          'div',
          { class: 'flex items-center' },
          [
            h(NIcon, { size: 20, class: 'mr-2' }, { default: () => h(TheIcon, { icon: getFileIcon(row.mime_type) }) }),
            h('span', {}, row.name)
          ]
        )
      }
    }
  },
  {
    title: '类型',
    key: 'mime_type',
    width: 120,
    render(row) {
      if (row.is_directory) {
        return '文件夹'
      }
      return row.mime_type || '-'
    }
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '大小',
    key: 'size_bytes',
    width: 100,
    render(row) {
      if (row.is_directory) {
        return '-'
      }
      return formatSize(row.size_bytes)
    }
  },
  {
    title: '修改时间',
    key: 'updated_at',
    width: 200,
    render(row) {
      return formatDate(row.updated_at)
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    fixed: 'right',
    render(row) {
      return [
        // Edit button
        withDirectives(
          h(
            NButton,
            {
              size: 'tiny',
              quaternary: true,
              type: 'info',
              onClick: () => handleEdit(row),
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit-outline', { size: 16 }),
            }
          ),
          [[vPermission, 'put/api/v1/file/{file_id}']]
        ),
        // Download button (only for files)
        !row.is_directory && withDirectives(
          h(
            NButton,
            {
              size: 'tiny',
              quaternary: true,
              type: 'success',
              onClick: () => handleDownload(row),
            },
            {
              default: () => '下载',
              icon: renderIcon('material-symbols:download', { size: 16 }),
            }
          ),
          [[vPermission, 'get/api/v1/file/{file_id}/download-url']]
        ),
        // Delete button
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }),
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'tiny',
                    quaternary: true,
                    type: 'error',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/file/{file_id}']]
              ),
            default: () => h('div', {}, '确定删除该文件吗?'),
          }
        ),
      ]
    }
  }
]

// Get file icon based on mime type
function getFileIcon(mimeType) {
  if (!mimeType) return 'mdi:file-outline'

  if (mimeType.startsWith('image/')) return 'mdi:file-image-outline'
  if (mimeType.startsWith('video/')) return 'mdi:file-video-outline'
  if (mimeType.startsWith('audio/')) return 'mdi:file-music-outline'
  if (mimeType.includes('pdf')) return 'mdi:file-pdf-outline'
  if (mimeType.includes('word') || mimeType.includes('document')) return 'mdi:file-word-outline'
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'mdi:file-excel-outline'
  if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'mdi:file-powerpoint-outline'
  if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'mdi:file-zip-outline'
  if (mimeType.includes('text')) return 'mdi:file-document-outline'

  return 'mdi:file-outline'
}


// Navigate to a directory
async function navigateToDirectory(directory) {
  try {
    // First update the parent_id in tableParams
    navigationPath.value.push({...directory})

    // Force a reactive update to ensure tableParams is updated
    await nextTick()

    // Now refresh the table with the new parent_id
    if ($table.value) {
      console.log('Navigating to directory:', directory.id, 'Current params:', tableParams.value)
      await $table.value.handleSearch()
    }
  } catch (error) {
    console.error('Error navigating to directory:', error)
    message.error('导航到目录失败')
    // Revert navigation path on error
    navigationPath.value.pop()
  }
}

// Navigate to a specific level in the path
async function navigateToPath(index) {
  try {
    // Store the original path in case we need to revert
    const originalPath = [...navigationPath.value]

    // If index is -1, go to root
    if (index === -1) {
      navigationPath.value = []
    } else {
      // Otherwise, truncate the path to the specified index (inclusive)
      navigationPath.value = navigationPath.value.slice(0, index + 1)
    }

    // Force a reactive update to ensure tableParams is updated
    await nextTick()

    // Refresh the table
    if ($table.value) {
      console.log('Navigating to path index:', index, 'Current params:', tableParams.value)
      await $table.value.handleSearch()
    }
  } catch (error) {
    console.error('Error navigating to path:', error)
    message.error('导航失败')
    // Revert to original path on error
    navigationPath.value = [...originalPath]
  }
}

// Handle file download
async function handleDownload(file) {
  try {
    const response = await fileApi.getDownloadUrl(file.id)
    if (response && response.data) {
      // Open the download URL in a new tab
      window.open(response.data, '_blank')
    }
  } catch (error) {
    console.error('Error getting download URL:', error)
    // Use the framework's error handling
    window.$message?.error('获取下载链接失败')
  }
}

// 打开上传模态框
function handleUpload() {
  // Ensure navigationPath is initialized
  if (!navigationPath.value) {
    navigationPath.value = []
  }

  // 重置上传状态
  resetUpload()

  // 显示上传模态框
  showUploadModal.value = true

  // 打印当前目录和 token 信息
  console.log('Current upload directory:', currentDirId.value)
  console.log('Current token:', getToken())
}

// 处理文件选择
function handleFileChange(event) {
  const files = event.target.files
  if (files && files.length > 0) {
    selectedFile.value = files[0]
    console.log('Selected file:', selectedFile.value.name, selectedFile.value.type, selectedFile.value.size)

    // 重置上传状态
    uploadProgress.value = 0
    uploadMessage.value = ''
    uploadSuccess.value = false
  }
}

// 重置上传状态
function resetUpload() {
  selectedFile.value = null
  uploadProgress.value = 0
  uploadMessage.value = ''
  uploadSuccess.value = false
  isUploading.value = false

  // 重置文件输入
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 提交文件上传
async function submitUpload() {
  if (!selectedFile.value) {
    window.$message?.warning('请先选择文件')
    return
  }

  try {
    isUploading.value = true
    uploadProgress.value = 0
    uploadMessage.value = ''
    uploadSuccess.value = false

    // 创建 FormData
    const formData = new FormData()
    formData.append('file', selectedFile.value)

    // 添加 parent_id 参数
    const parentId = uploadData.value.parent_id
    if (parentId !== undefined && parentId !== null) {
      formData.append('parent_id', String(parentId))
    }

    // 打印上传信息
    console.log('Uploading file:', selectedFile.value.name)
    console.log('Upload parent_id:', parentId)

    // 创建 XMLHttpRequest
    const xhr = new XMLHttpRequest()

    // 监听上传进度
    xhr.upload.onprogress = (event) => {
      if (event.lengthComputable) {
        const percentComplete = Math.round((event.loaded / event.total) * 100)
        uploadProgress.value = percentComplete
        console.log(`Upload progress: ${percentComplete}%`)
      }
    }

    // 处理上传完成
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Upload response:', response)

          if (response.code === 200) {
            uploadSuccess.value = true
            uploadMessage.value = response.msg || '文件上传成功'
            // 保留成功消息的全局提示，因为这是一个积极的反馈
            window.$message?.success(uploadMessage.value)

            // 刷新文件列表
            $table.value?.handleSearch()

            // 延迟关闭模态框
            setTimeout(() => {
              showUploadModal.value = false
              resetUpload()
            }, 1000)
          } else {
            uploadSuccess.value = false
            // 显示详细的错误信息
            uploadMessage.value = response.msg || '文件上传失败'
            // 如果有详细错误信息，显示在对话框中
            if (response.error) {
              uploadMessage.value += `: ${JSON.stringify(response.error)}`
            }
            // 不再显示全局错误消息，因为对话框中已经显示了详细信息
          }
        } catch (error) {
          console.error('Error parsing response:', error)
          uploadSuccess.value = false
          uploadMessage.value = `解析响应失败: ${error.message}\n原始响应: ${xhr.responseText.substring(0, 200)}...`
          // 不再显示全局错误消息
        }
      } else {
        uploadSuccess.value = false

        // 尝试解析错误响应
        try {
          const errorResponse = JSON.parse(xhr.responseText)
          uploadMessage.value = `上传失败 (${xhr.status}): ${errorResponse.msg || xhr.statusText}`

          // 如果有详细的错误信息，添加到消息中
          if (errorResponse.detail) {
            uploadMessage.value += `\n详细信息: ${errorResponse.detail}`
          }
        } catch (e) {
          // 如果无法解析 JSON，显示原始响应文本
          uploadMessage.value = `上传失败 (${xhr.status} ${xhr.statusText})\n响应: ${xhr.responseText.substring(0, 200)}...`
        }

        // 不再显示全局错误消息，因为对话框中已经显示了详细信息
      }

      isUploading.value = false
    }

    // 处理上传错误
    xhr.onerror = (event) => {
      console.error('Upload error:', event)
      uploadSuccess.value = false

      // 提供更详细的错误信息
      uploadMessage.value = '网络错误，上传失败'

      // 如果有更多错误信息，添加到消息中
      if (xhr.status) {
        uploadMessage.value += `\n状态码: ${xhr.status}`
      }

      if (xhr.statusText) {
        uploadMessage.value += `\n状态信息: ${xhr.statusText}`
      }

      // 尝试获取更多错误详情
      try {
        if (xhr.responseText) {
          uploadMessage.value += `\n响应: ${xhr.responseText.substring(0, 200)}...`
        }
      } catch (e) {
        // 忽略获取响应文本时的错误
      }

      // 不再显示全局错误消息，因为对话框中已经显示了详细信息
      isUploading.value = false
    }

    // 打开连接
    xhr.open('POST', '/api/v1/file/upload', true)

    // 设置请求头
    xhr.setRequestHeader('token', getToken())

    // 发送请求
    xhr.send(formData)
  } catch (error) {
    console.error('Error during upload:', error)
    uploadSuccess.value = false

    // 提供详细的错误信息
    uploadMessage.value = `上传失败: ${error.message}`

    // 如果有堆栈信息，添加到日志中（但不显示给用户）
    if (error.stack) {
      console.error('Error stack:', error.stack)
    }

    // 如果错误有更多属性，添加到消息中
    if (error.code) {
      uploadMessage.value += `\n错误代码: ${error.code}`
    }

    if (error.name && error.name !== 'Error') {
      uploadMessage.value += `\n错误类型: ${error.name}`
    }

    // 不再显示全局错误消息，因为对话框中已经显示了详细信息
    isUploading.value = false
  }
}

// Get current directory name for display
function getCurrentDirectoryName() {
  if (!navigationPath.value || navigationPath.value.length === 0) {
    return '根目录'
  }

  const currentDir = navigationPath.value[navigationPath.value.length - 1]
  return currentDir && currentDir.name ? currentDir.name : '根目录'
}

// Create new directory
function handleCreateDirectory() {
  modalForm.value = {
    ...initForm,
    parent_id: currentDirId.value,
    original_name: '' // Will be set when user enters a name
  }
  handleAdd()
}

// Initialize component
onMounted(async () => {
  try {
    // Reset navigation path to root
    navigationPath.value = []

    // Add a small delay to ensure the component is fully mounted
    await new Promise(resolve => setTimeout(resolve, 200))

    // Load data
    if ($table.value) {
      await $table.value.handleSearch()
    }
  } catch (error) {
    console.error('Error initializing file manager:', error)
    message.error('初始化文件管理器失败')
  }
})
</script>

<template>
  <!-- Main page -->
  <CommonPage show-footer title="文件管理">
    <!-- Action buttons -->
    <template #action>
      <div flex gap-2>
        <NButton
          v-permission="'post/api/v1/file/directory'"
          type="primary"
          @click="handleCreateDirectory"
        >
          <TheIcon icon="mdi:folder-plus" :size="18" class="mr-5" />新建文件夹
        </NButton>

        <NButton
          v-permission="'post/api/v1/file/upload'"
          type="info"
          @click="handleUpload"
        >
          <TheIcon icon="mdi:upload" :size="18" class="mr-5" />上传文件
        </NButton>

        <!-- File upload modal -->
        <NModal
          v-model:show="showUploadModal"
          title="上传文件"
          preset="card"
          style="width: 600px"
        >
          <div>
            <!-- 简单的文件上传表单 -->
            <form ref="uploadForm" enctype="multipart/form-data">
              <input
                type="file"
                ref="fileInput"
                @change="handleFileChange"
                style="margin-bottom: 16px;"
              />

              <div style="margin-bottom: 16px;">
                <NText depth="3">
                  当前上传目录: <span style="font-weight: bold;">{{ getCurrentDirectoryName() }}</span>
                </NText>
              </div>

              <!-- 显示选择的文件 -->
              <div v-if="selectedFile" style="margin-bottom: 16px;">
                <NTag type="info">{{ selectedFile.name }} ({{ formatSize(selectedFile.size) }})</NTag>
              </div>

              <!-- 上传进度 -->
              <div v-if="uploadProgress > 0 && uploadProgress < 100" style="margin-bottom: 16px;">
                <NProgress
                  type="line"
                  :percentage="uploadProgress"
                  :height="12"
                  :processing="uploadProgress < 100"
                />
              </div>

              <!-- 上传状态消息 -->
              <div v-if="uploadMessage" style="margin-bottom: 16px;">
                <div v-if="uploadSuccess" class="success-message">
                  <NTag type="success">{{ uploadMessage }}</NTag>
                </div>
                <div v-else class="error-message">
                  <NText type="error" style="white-space: pre-line; font-weight: bold;">{{ uploadMessage }}</NText>
                </div>
              </div>
            </form>
          </div>

          <template #footer>
            <div style="display: flex; justify-content: flex-end; gap: 8px;">
              <NButton @click="resetUpload">清空</NButton>
              <NButton
                type="primary"
                @click="submitUpload"
                :disabled="!selectedFile || isUploading"
                :loading="isUploading"
              >
                上传文件
              </NButton>
              <NButton type="default" @click="showUploadModal = false">关闭</NButton>
            </div>
          </template>
        </NModal>
      </div>
    </template>

    <!-- Breadcrumb navigation -->
    <div mb-4>
      <NBreadcrumb>
        <NBreadcrumbItem>
          <NButton text @click="navigateToPath(-1)">
            <TheIcon icon="mdi:home" :size="16" />
            <span ml-1>根目录</span>
          </NButton>
        </NBreadcrumbItem>

        <NBreadcrumbItem
          v-for="(item, index) in navigationPath"
          :key="index"
        >
          <NButton text @click="navigateToPath(index)">
            {{ item.name || '未命名目录' }}
          </NButton>
        </NBreadcrumbItem>
      </NBreadcrumb>
    </div>

    <!-- File table -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="fileApi.getFiles"
      :extra-params="tableParams"
      @update:extra-params="$table?.handleSearch()"
    >
      <template #queryBar>
        <QueryBarItem label="文件名" :label-width="80">
          <NInput
            v-model:value="queryItems.name"
            clearable
            type="text"
            placeholder="请输入文件名"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- Create/Edit modal -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
      >
        <NFormItem
          label="名称"
          path="name"
          :rule="{
            required: true,
            message: '请输入名称',
            trigger: ['input', 'blur'],
          }"
        >
          <NInput v-model:value="modalForm.name" placeholder="请输入名称" />
        </NFormItem>

        <NFormItem
          label="描述"
          path="description"
        >
          <NInput
            v-model:value="modalForm.description"
            type="textarea"
            placeholder="请输入文件或目录描述"
          />
        </NFormItem>

        <!-- Add more form fields as needed -->
      </NForm>
    </CrudModal>
  </CommonPage>
</template>

<style scoped>
.file-icon {
  font-size: 24px;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.breadcrumb-container {
  margin-bottom: 16px;
}

.directory-item {
  cursor: pointer;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.file-card {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s;
}

.file-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.file-name {
  margin-top: 8px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 错误消息样式 */
.error-message {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  margin-bottom: 8px;
}

/* 成功消息样式 */
.success-message {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  margin-bottom: 8px;
}
</style>

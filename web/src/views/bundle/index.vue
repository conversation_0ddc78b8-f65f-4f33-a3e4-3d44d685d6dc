<script setup>
import { h, onMounted, onUnmounted, ref, resolveDirective, withDirectives } from 'vue'
import { NButton, NTag, NForm, NFormItem, NInput, NSelect, NPopconfirm, NTooltip, useMessage, NSpin } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import FileSelector from '@/components/file/FileSelector.vue'

import { formatDate, formatSize, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: 'Bundle管理' })

// Setup message service
const message = useMessage()

// Table reference and query state
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// Bundle types
const bundleTypes = ref([])

// File selector state
const fileSelector = ref(null)
const selectedBundleId = ref(null)
const selectedFileIds = ref([])

// Bundle expansion state
const expandedBundles = ref(new Set())
const bundleFiles = ref(new Map())
const loadingFiles = ref(new Set())

// Task polling state
const pollingIntervals = ref(new Map())

// Form initialization
const initForm = {
  name: '',
  description: '',
  bundle_type: 'general',
  file_ids: []
}

// Status configuration
const processingStatusMap = {
  NEW: {
    type: 'primary',
    text: '未处理',
    icon: 'mdi:file-document-outline',
    actionText: '开始处理',
    actionIcon: 'mdi:play',
    canAction: true
  },
  PENDING: {
    type: 'warning',
    text: '待处理',
    icon: 'mdi:clock-outline',
    actionText: '开始处理',
    actionIcon: 'mdi:play',
    canAction: true
  },
  PROCESSING: {
    type: 'info',
    text: '处理中',
    icon: 'mdi:progress-clock',
    actionText: '取消处理',
    actionIcon: 'mdi:cancel',
    canAction: true
  },
  COMPLETED: {
    type: 'success',
    text: '已完成',
    icon: 'mdi:check-circle-outline',
    actionText: '重新处理',
    actionIcon: 'mdi:restart',
    canAction: true
  },
  CANCELLED: {
    type: 'warning',
    text: '已取消',
    icon: 'mdi:cancel',
    actionText: '重新处理',
    actionIcon: 'mdi:restart',
    canAction: true
  },
  FAILED: {
    type: 'error',
    text: '失败',
    icon: 'mdi:alert-circle-outline',
    actionText: '重试',
    actionIcon: 'mdi:refresh',
    canAction: true
  }
}

// CRUD operations
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleAdd,
  handleDelete,
  handleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: 'Bundle',
  initForm,
  doCreate: api.createBundle,
  doUpdate: api.updateBundle,
  doDelete: api.deleteBundle,
  refresh: () => $table.value?.handleSearch(),
})

// Handle bundle status action
async function handleStatusAction(bundle) {
  const status = processingStatusMap[bundle.status_processing]
  if (!status) {
    message.error(`未知状态: ${bundle.status_processing}`)
    return
  }

  try {
    switch (bundle.status_processing) {
      case 'NEW':
      case 'PENDING':
      case 'CANCELLED':
      case 'FAILED':
        // Start bundle processing
        message.loading(`正在启动 Bundle 处理: ${bundle.name}`, { duration: 0 })
        const startResult = await api.processBundle(bundle.id)
        message.destroyAll()

        if (startResult.code === 200) {
          message.success(`Bundle 处理任务已启动: ${bundle.name}`)
          // Start polling for status updates
          startTaskStatusPolling(bundle.id, startResult.data.task_id)
        } else {
          message.error(`启动 Bundle 处理失败: ${startResult.msg || '未知错误'}`)
        }
        break

      case 'PROCESSING':
        // Stop bundle processing
        if (!bundle.process_task_id) {
          message.error('无法取消处理：缺少任务ID')
          return
        }

        message.loading(`正在取消 Bundle 处理: ${bundle.name}`, { duration: 0 })
        const stopResult = await api.stopTask(bundle.process_task_id)
        message.destroyAll()

        if (stopResult.code === 200) {
          message.success(`Bundle 处理已取消: ${bundle.name}`)
          // Refresh table to update status
          $table.value?.handleSearch()
        } else {
          message.error(`取消 Bundle 处理失败: ${stopResult.msg || '未知错误'}`)
        }
        break

      case 'COMPLETED':
        // Restart bundle processing
        message.loading(`正在启动 Bundle 重新处理: ${bundle.name}`, { duration: 0 })
        const restartResult = await api.processBundle(bundle.id)
        message.destroyAll()

        if (restartResult.code === 200) {
          message.success(`Bundle 重新处理任务已启动: ${bundle.name}`)
          // Start polling for status updates
          startTaskStatusPolling(bundle.id, restartResult.data.task_id)
        } else {
          message.error(`启动 Bundle 重新处理失败: ${restartResult.msg || '未知错误'}`)
        }
        break

      default:
        message.info(`处理 Bundle: ${bundle.name}, 状态: ${bundle.status_processing}`)
    }
  } catch (error) {
    message.destroyAll()
    console.error('Error handling status action:', error)
    message.error(`操作失败: ${error.message || '未知错误'}`)
  }
}

// Task status polling functions
function startTaskStatusPolling(bundleId, taskId) {
  // Clear any existing polling for this bundle
  stopTaskStatusPolling(bundleId)

  // Start polling every 2 seconds
  const intervalId = setInterval(async () => {
    try {
      const statusResult = await api.getTaskStatus(taskId)
      if (statusResult.code === 200) {
        const taskStatus = statusResult.data.status

        // Check if task is finished (success, failure, or cancelled)
        if (['SUCCESS', 'FAILURE', 'REVOKED'].includes(taskStatus)) {
          // Stop polling
          stopTaskStatusPolling(bundleId)

          // Refresh table to show updated status
          $table.value?.handleSearch()

          // Show completion message
          if (taskStatus === 'SUCCESS') {
            message.success(`Bundle 处理完成`)
          } else if (taskStatus === 'FAILURE') {
            message.error(`Bundle 处理失败`)
          } else if (taskStatus === 'REVOKED') {
            message.warning(`Bundle 处理已取消`)
          }
        }
      }
    } catch (error) {
      console.error('Error polling task status:', error)
      // Continue polling even if there's an error
    }
  }, 2000)

  // Store the interval ID
  pollingIntervals.value.set(bundleId, intervalId)
}

function stopTaskStatusPolling(bundleId) {
  const intervalId = pollingIntervals.value.get(bundleId)
  if (intervalId) {
    clearInterval(intervalId)
    pollingIntervals.value.delete(bundleId)
  }
}

// Cleanup polling intervals when component is unmounted
function cleanupPolling() {
  pollingIntervals.value.forEach((intervalId) => {
    clearInterval(intervalId)
  })
  pollingIntervals.value.clear()
}

// Render functions for table columns
function renderStatus(row) {
  const status = processingStatusMap[row.status_processing] || {
    type: 'default',
    text: row.status_processing,
    icon: 'mdi:help-circle-outline'
  }

  // Only show status icon with tooltip
  return h(
    NTooltip,
    { trigger: 'hover' },
    {
      trigger: () => h(
        NTag,
        { type: status.type, style: 'display: flex; align-items: center; justify-content: center; width: 24px; height: 24px; padding: 0;' },
        {
          default: () => h(TheIcon, { icon: status.icon, size: 16 })
        }
      ),
      default: () => status.text
    }
  )
}

function renderActions(row) {
  const status = processingStatusMap[row.status_processing]

  return h('div', { class: 'flex gap-2' }, [
    // Status action button (if available)
    status && status.canAction ? h(
      NButton,
      {
        size: 'tiny',
        quaternary: true,
        type: status.type,
        onClick: () => handleStatusAction(row),
      },
      {
        default: () => status.actionText,
        icon: renderIcon(status.actionIcon, { size: 16 })
      }
    ) : null,

    // Edit button
    h(
      NButton,
      {
        size: 'tiny',
        quaternary: true,
        type: 'info',
        onClick: () => handleEdit(row),
      },
      {
        default: () => '修改',
        icon: renderIcon('material-symbols:edit-outline', { size: 16 })
      }
    ),

    // Delete button
    h(
      NPopconfirm,
      {
        onPositiveClick: () => handleDelete(row),
      },
      {
        default: () => '确认删除？',
        trigger: () =>
          withDirectives(
            h(
              NButton,
              {
                size: 'tiny',
                quaternary: true,
                type: 'error',
              },
              {
                default: () => '删除',
                icon: renderIcon('material-symbols:delete-outline', { size: 16 })
              }
            ),
            [[vPermission, 'delete/api/v1/bundle/{bundle_id}']]
          ),
      }
    ),

    // Files button
    withDirectives(
      h(
        NButton,
        {
          size: 'tiny',
          quaternary: true,
          type: 'success',
          onClick: () => openFileSelector(row),
        },
        {
          default: () => '文件',
          icon: renderIcon('material-symbols:folder-open-outline', { size: 16 })
        }
      ),
      [[vPermission, 'get/api/v1/bundle/{bundle_id}/files']]
    ),
  ])
}

// Render bundle name with expansion indicator or file name
function renderBundleName(row) {
  // Check if this is a file row
  if (row._isFile) {
    return h('div', {
      style: 'display: flex; align-items: center; padding-left: 26px; font-size: 13px; color: #666;'
    }, [
      // File icon
      h(TheIcon, {
        icon: row.file_type === 'directory' ? 'material-symbols:folder' : 'material-symbols:description',
        size: 16,
        style: 'margin-right: 8px; color: #666;'
      }),
      // File name
      h('span', { style: 'flex: 1;' }, row.original_name || row.name)
    ])
  }

  // Bundle row
  const isExpanded = expandedBundles.value.has(row.id)
  const isLoading = loadingFiles.value.has(row.id)

  return h('div', {
    style: 'display: flex; align-items: center;'
  }, [
    // Expansion icon - only this part is clickable
    h('div', {
      style: 'display: flex; align-items: center; cursor: pointer; padding: 4px; margin-right: 4px;',
      onClick: (e) => {
        e.stopPropagation()
        handleBundleClick(row)
      }
    }, [
      h(TheIcon, {
        icon: 'material-symbols:chevron-right',
        size: 18,
        style: `color: #666; transform: ${isExpanded ? 'rotate(90deg)' : 'rotate(0deg)'}; transition: transform 0.2s ease;`
      })
    ]),
    // Loading spinner
    isLoading ? h(NSpin, { size: 'small', style: 'margin-right: 8px;' }) : null,
    // Bundle name - not clickable
    h('span', { style: 'flex: 1; user-select: none;' }, row.name)
  ])
}



// Render type column for both bundles and files
function renderType(row) {
  if (row._isFile) {
    // File row - show file type and size
    return h('div', { style: 'font-size: 12px; color: #666;' }, [
      h('div', {}, row.file_type || 'file'),
      row.file_size ? h('div', { style: 'color: #999; margin-top: 2px;' }, formatSize(row.file_size)) : null
    ])
  }
  // Bundle row
  return h(NTag, { type: 'info' }, { default: () => row.bundle_type })
}

// Render status column for bundles only
function renderStatusColumn(row) {
  if (row._isFile) {
    // File row - show creation date
    return h('span', { style: 'font-size: 12px; color: #666;' },
      row.created_at ? formatDate(row.created_at) : ''
    )
  }
  // Bundle row
  return renderStatus(row)
}

// Render description column
function renderDescription(row) {
  if (row._isFile) {
    // File row - show file path or storage path
    return h('span', {
      style: 'font-size: 12px; color: #666; font-style: italic;'
    }, row.storage_path || '')
  }
  // Bundle row
  return row.description || ''
}

// Render updated time column
function renderUpdatedTime(row) {
  if (row._isFile) {
    // File row - empty or show file modification time
    return ''
  }
  // Bundle row
  return formatDate(row.updated_at)
}

// Render actions column for bundles only
function renderActionsColumn(row) {
  if (row._isFile) {
    // File row - no actions
    return null
  }
  // Bundle row
  return renderActions(row)
}

// Table columns definition
const columns = [
  {
    title: '名称',
    key: 'name',
    width: 200,
    render: renderBundleName
  },
  {
    title: '类型',
    key: 'bundle_type',
    width: 120,
    render: renderType
  },
  {
    title: '状态',
    key: 'status_processing',
    width: 100,
    render: renderStatusColumn
  },
  {
    title: '描述',
    key: 'description',
    width: 250,
    ellipsis: { tooltip: true },
    render: renderDescription
  },
  {
    title: '修改时间',
    key: 'updated_at',
    width: 200,
    render: renderUpdatedTime
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: renderActionsColumn
  }
]

// Load bundle types
async function loadBundleTypes() {
  try {
    const { data } = await api.getBundleTypes()
    bundleTypes.value = data
  } catch (error) {
    console.error('Error loading bundle types:', error)
    message.error('加载Bundle类型失败')
  }
}

// Open file selector for a bundle
async function openFileSelector(bundle) {
  try {
    selectedBundleId.value = bundle.id

    // Load bundle files
    const { data } = await api.getBundleFiles(bundle.id)
    selectedFileIds.value = data.map(file => file.id)

    // Open file selector
    fileSelector.value.open()
  } catch (error) {
    console.error('Error loading bundle files:', error)
    message.error('加载Bundle文件失败')
  }
}

// Handle file selection confirmation
async function handleFileSelectionConfirm(result) {
  if (!result.modified || !selectedBundleId.value) return

  try {
    // Update bundle with selected files
    await api.updateBundle({
      id: selectedBundleId.value,
      file_ids: result.selectedIds
    })

    message.success('Bundle文件更新成功')
    $table.value?.handleSearch()
  } catch (error) {
    console.error('Error updating bundle files:', error)
    message.error('更新Bundle文件失败')
  }
}

// Handle bundle expansion/collapse
async function handleBundleClick(bundle) {
  const bundleId = bundle.id

  if (expandedBundles.value.has(bundleId)) {
    // Collapse bundle
    expandedBundles.value.delete(bundleId)
    bundleFiles.value.delete(bundleId)
    // Refresh table to remove file rows
    await refreshTableData()
  } else {
    // Expand bundle and load files
    expandedBundles.value.add(bundleId)
    await loadBundleFiles(bundleId)
  }
}

// Load files for a specific bundle
async function loadBundleFiles(bundleId) {
  if (loadingFiles.value.has(bundleId)) return

  try {
    loadingFiles.value.add(bundleId)
    const { data } = await api.getBundleFiles(bundleId)
    bundleFiles.value.set(bundleId, data)
    // Refresh table to show file rows
    await refreshTableData()
  } catch (error) {
    console.error('Error loading bundle files:', error)
    message.error('加载Bundle文件失败')
    // Remove from expanded if loading failed
    expandedBundles.value.delete(bundleId)
  } finally {
    loadingFiles.value.delete(bundleId)
  }
}

// Custom data getter that applies expansion logic
async function getBundlesWithFiles(params) {
  // Get bundle data from API
  const result = await api.getBundles(params)
  const bundles = result.data || []

  // Apply expansion logic
  const expandedData = []
  for (const bundle of bundles) {
    // Add the bundle row
    expandedData.push(bundle)

    // If bundle is expanded, add file rows
    if (expandedBundles.value.has(bundle.id)) {
      const files = bundleFiles.value.get(bundle.id) || []
      for (const file of files) {
        // Create a file row with special properties to identify it as a file
        expandedData.push({
          ...file,
          _isFile: true,
          _bundleId: bundle.id,
          id: `file_${file.id}`, // Unique ID for the table
          _originalId: file.id // Keep original file ID
        })
      }
    }
  }

  return {
    ...result,
    data: expandedData
  }
}

// Refresh table data with expanded files
async function refreshTableData() {
  if ($table.value) {
    await $table.value.handleSearch()
  }
}

// Initialize component
onMounted(async () => {
  await loadBundleTypes()
  $table.value?.handleSearch()
})

// Cleanup when component is unmounted
onUnmounted(() => {
  cleanupPolling()
})
</script>

<template>
  <!-- Main page -->
  <CommonPage show-footer title="Bundle管理">
    <!-- Action buttons -->
    <template #action>
      <NButton
        v-permission="'post/api/v1/bundle/create'"
        type="primary"
        @click="handleAdd"
      >
        <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建Bundle
      </NButton>
    </template>

    <!-- Bundle table -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getBundlesWithFiles"
    >
      <template #queryBar>
        <QueryBarItem label="名称" :label-width="80">
          <NInput
            v-model:value="queryItems.name"
            clearable
            type="text"
            placeholder="请输入Bundle名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>

        <QueryBarItem label="类型" :label-width="80">
          <NSelect
            v-model:value="queryItems.bundle_type"
            clearable
            placeholder="请选择Bundle类型"
            :options="bundleTypes"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- Create/Edit modal -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
      >
        <NFormItem
          label="名称"
          path="name"
          :rule="{
            required: true,
            message: '请输入名称',
            trigger: ['input', 'blur'],
          }"
        >
          <NInput v-model:value="modalForm.name" placeholder="请输入名称" />
        </NFormItem>

        <NFormItem
          label="类型"
          path="bundle_type"
        >
          <NSelect
            v-model:value="modalForm.bundle_type"
            placeholder="请选择Bundle类型"
            :options="bundleTypes"
          />
        </NFormItem>

        <NFormItem
          label="描述"
          path="description"
        >
          <NInput
            v-model:value="modalForm.description"
            type="textarea"
            placeholder="请输入Bundle描述"
          />
        </NFormItem>
      </NForm>
    </CrudModal>

    <!-- File selector component -->
    <FileSelector
      ref="fileSelector"
      :selected-files="selectedFileIds"
      :rootdir="null"
      title="选择Bundle文件"
      @confirm="handleFileSelectionConfirm"
    />
  </CommonPage>
</template>

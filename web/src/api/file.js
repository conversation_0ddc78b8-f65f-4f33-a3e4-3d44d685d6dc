import { request } from '@/utils'

export default {
  // List files and directories
  getFiles: (params) => request.get('/file/list', { params }),

  // Get file details
  getFileInfo: (fileId) => request.get(`/file/${fileId}`),

  // Upload file - 简化版本，让 NUpload 组件直接使用 action
  uploadFile: (formData) => request.post('/file/upload', formData),

  // Create directory
  createDirectory: (data) => request.post('/file/directory', data),

  // Update file or directory
  updateFile: (data) => {
    const id = data.id
    return request.put(`/file/${id}`, data)
  },

  // Delete file or directory
  deleteFile: (params) => {
    const id = params.id
    return request.delete(`/file/${id}`)
  },

  // Get download URL by file ID
  getDownloadUrl: (fileId, expiresSeconds = 3600) =>
    request.get('/file/download-url', { params: { file_id: fileId, expires_seconds: expiresSeconds } }),

  // Get download URL by page ID
  getDownloadUrlByPageId: (pageId, expiresSeconds = 3600) =>
    request.get('/file/download-url', { params: { page_id: pageId, expires_seconds: expiresSeconds } }),
}

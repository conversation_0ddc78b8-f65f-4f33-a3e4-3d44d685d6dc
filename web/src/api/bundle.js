import { request } from '@/utils'

export default {
  // List bundles with optional filtering
  getBundles: (params) => request.get('/bundle/list', { params }),

  // Get bundle details
  getBundleInfo: (bundleId) => request.get(`/bundle/${bundleId}`),

  // Create a new bundle
  createBundle: (data) => request.post('/bundle/create', data),

  // Update bundle
  updateBundle: (data) => request.post('/bundle/update', data),

  // Delete bundle (soft delete)
  deleteBundle: (params) => request.delete(`/bundle/${params.id}`),

  // Get files associated with a bundle
  getBundleFiles: (bundleId) => request.get(`/bundle/${bundleId}/files`),

  // Get pages associated with a bundle
  getBundlePages: (bundleId) => request.get(`/bundle/${bundleId}/pages`),

  // Get available bundle types
  getBundleTypes: () => request.get('/bundle/types'),
}

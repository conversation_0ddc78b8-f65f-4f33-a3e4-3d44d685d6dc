import { request } from '@/utils'

export default {
  // Start a task using the unified task management API
  startTask: (taskName, params) => request.post('/task/start', {
    task_name: taskName,
    params: params
  }),

  // Process a bundle (convenience method)
  processBundle: (bundleId) => request.post('/task/start', {
    task_name: 'bundle_process',
    params: { bundle_id: bundleId }
  }),

  // Process a page (convenience method)
  processPage: (pageId) => request.post('/task/start', {
    task_name: 'page_process',
    params: { page_id: pageId }
  }),

  // Get task status
  getTaskStatus: (taskId) => request.get(`/task/${taskId}/status`),

  // Stop/cancel a task
  stopTask: (taskId) => request.post(`/task/${taskId}/stop`),

  // Get available task types
  getTaskTypes: () => request.get('/task/types'),
}

import { request } from '@/utils'

export default {
  // List pages with optional filtering
  getPages: (params) => request.get('/page/list', { params }),

  // Get page details
  getPageInfo: (pageId) => request.get(`/page/${pageId}`),

  // Create a new page
  createPage: (data) => request.post('/page/create', data),

  // Update page
  updatePage: (data) => request.post('/page/update', data),

  // Delete page
  deletePage: (pageId) => request.delete(`/page/${pageId}`),
}

<template>
  <div class="vditor-editor-wrapper">
    <div ref="editorContainer" class="vditor-editor-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import Vditor from 'vditor'
import 'vditor/dist/index.css'

defineOptions({ name: 'VditorEditor' })

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  mode: {
    type: String,
    default: 'wysiwyg',
    validator: (value) => ['wysiwyg', 'ir', 'sv'].includes(value)
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  height: {
    type: [String, Number],
    default: 400
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  theme: {
    type: String,
    default: 'classic',
    validator: (value) => ['classic', 'dark'].includes(value)
  },
  preview: {
    type: <PERSON>olean,
    default: false
  },
  previewOnly: {
    type: <PERSON>ole<PERSON>,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'error'])

const editorContainer = ref(null)
let vditor = null

// Initialize the Vditor editor
function initializeEditor() {
  if (!editorContainer.value) return

  try {
    // Destroy existing editor if it exists
    if (vditor) {
      vditor.destroy()
      vditor = null
    }

    const options = {
      element: editorContainer.value,
      height: typeof props.height === 'number' ? props.height : parseInt(props.height),
      mode: props.mode,
      theme: props.theme,
      placeholder: props.placeholder,
      value: props.modelValue || '',
      cache: {
        enable: false
      },
      preview: {
        mode: 'both',
        hljs: {
          style: 'github'
        },
        actions: [] // 移除预览操作按钮
      },
      toolbar: [
        // 自定义工具栏，移除不需要的按钮
        'emoji',
        'headings',
        'bold',
        'italic',
        'strike',
        'link',
        '|',
        'list',
        'ordered-list',
        'check',
        'outdent',
        'indent',
        '|',
        'quote',
        'line',
        'code',
        'inline-code',
        'insert-before',
        'insert-after',
        '|',
        'upload',
        'table',
        '|',
        'undo',
        'redo',
        '|',
        'fullscreen',
        'edit-mode'
      ],
      input: (value) => {
        emit('update:modelValue', value)
        emit('change', value)
      },
      after: () => {
        if (props.readOnly) {
          vditor.disabled()
        }

        // 修复预览功能调用
        if (props.preview) {
          // 确保 vditor 已完全初始化并且 preview 方法存在
          setTimeout(() => {
            if (vditor && typeof vditor.preview === 'function') {
              vditor.preview()
            } else if (vditor && typeof vditor.setPreviewMode === 'function') {
              // 某些版本可能使用不同的方法名
              vditor.setPreviewMode('preview')
            }
          }, 100)
        }
      }
    }

    // Create new editor instance
    vditor = new Vditor(editorContainer.value, options)
  } catch (error) {
    console.error('Error initializing Vditor editor:', error)
    emit('error', error)
  }
}

// Update editor content when modelValue changes
function updateEditorContent() {
  if (!vditor) return

  try {
    const currentValue = vditor.getValue()
    if (currentValue !== props.modelValue) {
      vditor.setValue(props.modelValue || '')
    }
  } catch (error) {
    console.error('Error updating Vditor content:', error)
    emit('error', error)
  }
}

// Watch for changes in modelValue
watch(() => props.modelValue, () => {
  updateEditorContent()
})

// Watch for readOnly changes
watch(() => props.readOnly, (newReadOnly) => {
  if (vditor) {
    if (newReadOnly) {
      vditor.disabled()
    } else {
      vditor.enable()
    }
  }
})

// Watch for preview mode changes
watch(() => props.preview, (newPreview) => {
  if (vditor) {
    setTimeout(() => {
      try {
        if (newPreview) {
          if (typeof vditor.preview === 'function') {
            vditor.preview()
          } else if (typeof vditor.setPreviewMode === 'function') {
            vditor.setPreviewMode('preview')
          }
        } else {
          if (typeof vditor.unpreview === 'function') {
            vditor.unpreview()
          } else if (typeof vditor.setPreviewMode === 'function') {
            vditor.setPreviewMode('editor')
          }
        }
      } catch (error) {
        console.warn('Error setting preview mode:', error)
      }
    }, 100)
  }
})

// Lifecycle hooks
onMounted(async () => {
  await nextTick()
  initializeEditor()
})

onUnmounted(() => {
  if (vditor) {
    vditor.destroy()
    vditor = null
  }
})

// Expose methods for parent components
defineExpose({
  getEditor: () => vditor,
  refresh: initializeEditor,
  getValue: () => vditor?.getValue() || '',
  setValue: (value) => vditor?.setValue(value || ''),
  disabled: () => vditor?.disabled(),
  enable: () => vditor?.enable(),
  preview: () => vditor?.preview(),
  unpreview: () => vditor?.unpreview()
})
</script>

<style scoped>
.vditor-editor-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.vditor-editor-container {
  width: 100%;
  flex: 1;
  min-height: v-bind('typeof props.height === "number" ? `${props.height}px` : props.height');
}

/* 添加全局样式以确保 Vditor 填满容器 */
:deep(.vditor) {
  height: 100% !important;
  display: flex;
  flex-direction: column;
}

:deep(.vditor-content) {
  flex: 1;
  height: auto !important;
}

:deep(.vditor-preview) {
  flex: 1;
  height: auto !important;
}

/* 隐藏设备预览工具栏 */
:deep(.vditor-preview__action) {
  display: none !important;
}

/* 确保预览模式下内容区域填满 */
:deep(.vditor-preview__main) {
  height: 100% !important;
}
</style>

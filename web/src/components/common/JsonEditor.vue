<template>
  <div class="json-editor-wrapper">
    <div ref="editorContainer" class="json-editor-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { createJSONEditor } from 'vanilla-jsoneditor'

defineOptions({ name: 'JsonEditor' })

const props = defineProps({
  modelValue: {
    type: [Object, Array, String],
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'tree',
    validator: (value) => ['tree', 'text', 'table'].includes(value)
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  mainMenuBar: {
    type: Boolean,
    default: true
  },
  navigationBar: {
    type: Boolean,
    default: true
  },
  statusBar: {
    type: Boolean,
    default: true
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'error'])

const editorContainer = ref(null)
let jsonEditor = null

// Convert value to proper format for the editor
function prepareContent(value) {
  if (!value) return { json: {} }
  
  try {
    if (typeof value === 'string') {
      const trimmed = value.trim()
      if (!trimmed || trimmed === '{}' || trimmed === '[]') {
        return { json: {} }
      }
      // Try to parse as JSON
      const parsed = JSON.parse(trimmed)
      return { json: parsed }
    } else {
      return { json: value }
    }
  } catch (error) {
    // If parsing fails, treat as text
    return { text: typeof value === 'string' ? value : JSON.stringify(value, null, 2) }
  }
}

// Initialize the JSON editor
function initializeEditor() {
  if (!editorContainer.value) return

  try {
    // Dispose existing editor if it exists
    if (jsonEditor) {
      jsonEditor.destroy()
      jsonEditor = null
    }

    // Create new editor instance
    jsonEditor = createJSONEditor({
      target: editorContainer.value,
      props: {
        mode: props.mode,
        mainMenuBar: props.mainMenuBar,
        navigationBar: props.navigationBar,
        statusBar: props.statusBar,
        readOnly: props.readOnly,
        content: prepareContent(props.modelValue),
        onChange: (updatedContent, previousContent, { contentErrors, patchResult }) => {
          try {
            // Extract the actual data from the editor content
            let newValue
            if (updatedContent.json !== undefined) {
              newValue = updatedContent.json
            } else if (updatedContent.text !== undefined) {
              try {
                newValue = JSON.parse(updatedContent.text)
              } catch (e) {
                newValue = updatedContent.text
              }
            } else {
              newValue = {}
            }

            // Emit the change events
            emit('update:modelValue', newValue)
            emit('change', newValue, contentErrors)
          } catch (error) {
            console.error('Error handling JSON editor change:', error)
            emit('error', error)
          }
        },
        onError: (err) => {
          console.error('JSON Editor error:', err)
          emit('error', err)
        }
      }
    })
  } catch (error) {
    console.error('Error initializing JSON editor:', error)
    emit('error', error)
  }
}

// Update editor content when modelValue changes
function updateEditorContent() {
  if (!jsonEditor) return

  try {
    const newContent = prepareContent(props.modelValue)
    jsonEditor.set(newContent)
  } catch (error) {
    console.error('Error updating JSON editor content:', error)
    emit('error', error)
  }
}

// Watch for changes in modelValue
watch(() => props.modelValue, () => {
  updateEditorContent()
}, { deep: true })

// Watch for mode changes
watch(() => props.mode, () => {
  if (jsonEditor) {
    jsonEditor.updateProps({ mode: props.mode })
  }
})

// Watch for readOnly changes
watch(() => props.readOnly, () => {
  if (jsonEditor) {
    jsonEditor.updateProps({ readOnly: props.readOnly })
  }
})

// Lifecycle hooks
onMounted(async () => {
  await nextTick()
  initializeEditor()
})

onUnmounted(() => {
  if (jsonEditor) {
    jsonEditor.destroy()
    jsonEditor = null
  }
})

// Expose methods for parent components
defineExpose({
  getEditor: () => jsonEditor,
  refresh: initializeEditor,
  validate: () => {
    if (jsonEditor) {
      return jsonEditor.validate()
    }
    return []
  }
})
</script>

<style scoped>
.json-editor-wrapper {
  width: 100%;
  height: v-bind(height);
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.json-editor-container {
  width: 100%;
  height: 100%;
}

/* Import vanilla-jsoneditor styles */
:deep(.jse-main) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

:deep(.jse-menu) {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

:deep(.jse-tree-mode .jse-contents) {
  background-color: #ffffff;
}

:deep(.jse-text-mode .jse-contents) {
  background-color: #ffffff;
}

:deep(.jse-table-mode .jse-contents) {
  background-color: #ffffff;
}
</style>

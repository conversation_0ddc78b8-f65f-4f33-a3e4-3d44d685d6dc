<template>
  <NLayout has-sider wh-full>
    <!-- Left sidebar - Bundle Tree -->
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="300"
      show-trigger="arrow-circle"
      :collapsed="collapsed"
      @collapse="collapsed = true"
      @expand="collapsed = false"
    >
      <!-- Bundle Tree -->
      <div class="tree-container">
        <BundleTree
          :bundle-type="props.bundleType"
          :selected-page="selectedPage"
          @page-select="handlePageSelect"
          @page-edit="handlePageEdit"
        />
      </div>
    </NLayoutSider>

    <!-- Right content area -->
    <NLayoutContent>
      <div class="content-container">
        <PageContainer
          :page="selectedPage"
          :bundle-type="props.bundleType"
          :mode="currentMode"
          @mode-change="handleModeChange"
        />
      </div>
    </NLayoutContent>
  </NLayout>
</template>

<script setup>
import { ref } from 'vue'
import { NLayout, NLayoutSider, NLayoutContent } from 'naive-ui'
import BundleTree from '@/components/bundle/BundleTree.vue'
import PageContainer from '@/components/bundle/PageContainer.vue'

defineOptions({ name: 'BundleExplorer' })

// Props
const props = defineProps({
  bundleType: {
    type: String,
    required: true
  }
})

// Component state
const collapsed = ref(false)
const selectedPage = ref(null)
const currentMode = ref('preview') // 'preview' | 'edit'

// Event handlers
function handlePageSelect(page) {
  selectedPage.value = page
  currentMode.value = 'preview'
}

function handlePageEdit(page) {
  selectedPage.value = page
  currentMode.value = 'edit'
}

function handleModeChange(mode) {
  currentMode.value = mode
}
</script>

<style scoped>
.tree-container {
  height: 100%;
  overflow-y: auto;
}

.content-container {
  height: 100%;
  padding: 24px;
  overflow-y: auto;
}
</style>

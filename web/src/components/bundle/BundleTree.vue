<template>
  <div class="bundle-tree">
    <div class="tree-header">
      <div class="search-section">
        <NInput
          v-model:value="searchQuery"
          placeholder="搜索文集名称..."
          clearable
          size="small"
          class="search-input"
        >
          <template #prefix>
            <TheIcon icon="material-symbols:search" />
          </template>
        </NInput>
        <NButton
          size="small"
          type="primary"
          @click="refreshData"
          :loading="loading"
        >
          <template #icon>
            <TheIcon icon="material-symbols:refresh" />
          </template>
          刷新
        </NButton>
      </div>
    </div>

    <div class="tree-content">
      <NTree
        ref="treeRef"
        :data="treeData"
        :expanded-keys="expandedKeys"
        :selected-keys="selectedKeys"
        key-field="key"
        label-field="label"
        children-field="children"
        :node-props="nodeProps"
        :render-label="renderLabel"
        @update:expanded-keys="handleExpandedKeysChange"
        @update:selected-keys="handleSelectedKeysChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, h, watch } from 'vue'
import { NTree, NButton, NSpin, NInput, useMessage } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'
import api from '@/api'

defineOptions({ name: 'BundleTree' })

const props = defineProps({
  bundleType: {
    type: String,
    required: true
  },
  selectedPage: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['page-select', 'page-edit'])

// Component state
const message = useMessage()
const treeRef = ref(null)
const loading = ref(false)
const bundles = ref([])
const bundlePages = ref(new Map())
const expandedKeys = ref([])
const selectedKeys = ref([])
const searchQuery = ref('')

// Computed properties
const filteredBundles = computed(() => {
  if (!searchQuery.value.trim()) {
    return bundles.value
  }

  const query = searchQuery.value.toLowerCase().trim()
  return bundles.value.filter(bundle =>
    bundle.name.toLowerCase().includes(query)
  )
})

const treeData = computed(() => {
  return filteredBundles.value.map(bundle => ({
    key: `bundle-${bundle.id}`,
    label: bundle.name,
    type: 'bundle',
    data: bundle,
    children: getBundleChildren(bundle)
  }))
})

// Helper functions
function getBundleChildren(bundle) {
  const pages = bundlePages.value.get(bundle.id) || []
  return pages
    .filter(page => page.process_status === 'COMPLETED')
    .map(page => ({
      key: `page-${page.id}`,
      label: `第${page.page_number}页`,
      type: 'page',
      data: page,
      isLeaf: true
    }))
}

function nodeProps({ option }) {
  return {
    onClick() {
      if (option.type === 'page') {
        emit('page-select', option.data)
        selectedKeys.value = [option.key]
      }
    },
    onDblclick() {
      if (option.type === 'page') {
        emit('page-edit', option.data)
        selectedKeys.value = [option.key]
      }
    }
  }
}

function renderLabel({ option }) {
  const isLoading = option.type === 'bundle' && loading.value
  const childrenCount = option.type === 'bundle' ? getBundleChildren(option.data).length : 0

  return h('div', {
    style: 'display: flex; align-items: center; gap: 8px;'
  }, [
    isLoading ? h(NSpin, { size: 'small' }) : null,
    h('span', option.label),
    option.type === 'bundle' ? h('span', {
      style: 'color: #999; font-size: 12px;'
    }, `(${childrenCount}页)`) : null
  ])
}

// Event handlers
async function handleExpandedKeysChange(keys) {
  expandedKeys.value = keys

  for (const key of keys) {
    if (key.startsWith('bundle-')) {
      const bundleId = parseInt(key.replace('bundle-', ''))
      if (!bundlePages.value.has(bundleId)) {
        await loadBundlePages(bundleId)
      }
    }
  }
}

function handleSelectedKeysChange(keys) {
  selectedKeys.value = keys
}

// Data loading functions
async function loadBundles() {
  try {
    loading.value = true
    const response = await api.getBundles({
      bundle_type: props.bundleType,
      page: 1,
      page_size: 100
    })

    if (response.code === 200) {
      bundles.value = response.data.items || response.data || []
    } else {
      message.error(`加载Bundle列表失败: ${response.msg}`)
    }
  } catch (error) {
    console.error('Error loading bundles:', error)
    message.error('加载Bundle列表失败')
  } finally {
    loading.value = false
  }
}

async function loadBundlePages(bundleId) {
  try {
    const response = await api.getBundlePages(bundleId)

    if (response.code === 200) {
      bundlePages.value.set(bundleId, response.data || [])
    } else {
      message.error(`加载Bundle页面失败: ${response.msg}`)
    }
  } catch (error) {
    console.error('Error loading bundle pages:', error)
    message.error('加载Bundle页面失败')
  }
}

async function refreshData() {
  bundlePages.value.clear()
  expandedKeys.value = []
  selectedKeys.value = []
  await loadBundles()
}

// Lifecycle
onMounted(() => {
  loadBundles()
})
</script>

<style scoped>
.bundle-tree {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tree-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.search-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.search-input {
  flex: 1;
}

.tree-content {
  flex: 1;
  overflow-y: auto;
}
</style>
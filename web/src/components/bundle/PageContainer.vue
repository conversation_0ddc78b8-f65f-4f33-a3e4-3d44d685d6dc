<template>
  <div class="page-container">
    <!-- Empty state when no page is selected -->
    <div v-if="!page" class="empty-state">
      <div class="empty-content">
        <TheIcon icon="material-symbols:description-outline" :size="64" />
        <h3>请选择页面</h3>
        <p>单击页面节点进行预览，双击页面节点进行编辑</p>
      </div>
    </div>

    <!-- Page content when page is selected -->
    <div v-else class="page-content">
      <!-- Header with page info and mode toggle -->
      <div class="page-header">
        <div class="page-info">
          <h2>第{{ page.page_number }}页</h2>
          <NTag :type="getStatusType(page.process_status)">
            {{ getStatusText(page.process_status) }}
          </NTag>
        </div>

        <div class="page-actions">
          <NButtonGroup>
            <NButton
              :type="mode === 'preview' ? 'primary' : 'default'"
              @click="$emit('mode-change', 'preview')"
            >
              <template #icon>
                <TheIcon icon="material-symbols:visibility" />
              </template>
              预览
            </NButton>
            <NButton
              :type="mode === 'edit' ? 'primary' : 'default'"
              @click="$emit('mode-change', 'edit')"
            >
              <template #icon>
                <TheIcon icon="material-symbols:edit" />
              </template>
              编辑
            </NButton>
          </NButtonGroup>
        </div>
      </div>

      <!-- Dynamic component based on bundle type and mode -->
      <div class="component-container">
        <component
          :is="currentComponent"
          v-if="currentComponent"
          :page="page"
          :edit-mode="mode === 'edit'"
          @save="handleSave"
        />

        <!-- Fallback for unsupported bundle types -->
        <div v-else class="unsupported-type">
          <TheIcon icon="material-symbols:error-outline" :size="48" />
          <h3>不支持的Bundle类型</h3>
          <p>Bundle类型 "{{ bundleType }}" 暂不支持预览和编辑功能</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, markRaw } from 'vue'
import { NTag, NButton, NButtonGroup, useMessage } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'

// Import page components
import EducationPagePreviewer from '@/components/page/previewer/EducationPagePreviewer.vue'
import EducationPageEditor from '@/components/page/editor/EducationPageEditor.vue'
import GeneralPagePreviewer from '@/components/page/previewer/GeneralPagePreviewer.vue'
import GeneralPageEditor from '@/components/page/editor/GeneralPageEditor.vue'

defineOptions({ name: 'PageContainer' })

const props = defineProps({
  page: {
    type: Object,
    default: null
  },
  bundleType: {
    type: String,
    required: true
  },
  mode: {
    type: String,
    default: 'preview',
    validator: (value) => ['preview', 'edit'].includes(value)
  }
})

const emit = defineEmits(['mode-change'])
const message = useMessage()

// Component mapping for different bundle types
const previewerMap = {
  'education': markRaw(EducationPagePreviewer),
  'general': markRaw(GeneralPagePreviewer),
  'invoice': markRaw(GeneralPagePreviewer),
  'contract': markRaw(GeneralPagePreviewer)
}

const editorMap = {
  'education': markRaw(EducationPageEditor),
  'general': markRaw(GeneralPageEditor),
  'invoice': markRaw(GeneralPageEditor),
  'contract': markRaw(GeneralPageEditor)
}

// Computed properties
const currentComponent = computed(() => {
  if (!props.page) return null

  const componentMap = props.mode === 'preview' ? previewerMap : editorMap
  return componentMap[props.bundleType] || null
})

// Helper functions
function getStatusType(status) {
  const statusMap = {
    'NEW': 'default',
    'PENDING': 'warning',
    'PROCESSING': 'info',
    'COMPLETED': 'success',
    'CANCELLED': 'default',
    'FAILED': 'error'
  }
  return statusMap[status] || 'default'
}

function getStatusText(status) {
  const statusMap = {
    'NEW': '新建',
    'PENDING': '等待中',
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消',
    'FAILED': '失败'
  }
  return statusMap[status] || status
}

// Event handlers
function handleSave(data) {
  message.success('保存成功')
  // Emit save event to parent if needed
}
</script>

<style scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  color: #999;
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: #666;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
}

.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 16px;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.component-container {
  flex: 1;
  overflow-y: auto;
}

.unsupported-type {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #999;
}

.unsupported-type h3 {
  margin: 16px 0 8px 0;
  color: #666;
}

.unsupported-type p {
  margin: 0;
  font-size: 14px;
}
</style>

<template>
  <div class="general-page-previewer">
    <NTabs type="line" animated class="full-height-tabs">
      <!-- Image Tab -->
      <NTabPane name="image" tab="原始图像">
        <div class="image-container">
          <div v-if="page.image_url" class="image-wrapper">
            <img
              v-if="!isImageLoading && imageUrl"
              :src="imageUrl"
              :alt="`第${page.page_number}页`"
              class="page-image"
              @load="handleImageLoad"
              @error="handleImageError"
            />
            <div v-else-if="isImageLoading" class="loading-state">
              <TheIcon icon="material-symbols:loading" :size="48" class="loading-icon" />
              <p>加载中...</p>
            </div>
            <div class="image-info">
              <NTag size="small">
                {{ page.image_width }}×{{ page.image_height }}
              </NTag>
              <NTag size="small" v-if="page.image_ppi">
                {{ page.image_ppi }} PPI
              </NTag>
            </div>
          </div>
          <div v-else class="no-image">
            <TheIcon icon="material-symbols:image-not-supported-outline" :size="48" />
            <p>暂无图像</p>
          </div>
        </div>
      </NTabPane>

      <!-- OCR Results Tab -->
      <NTabPane name="ocr" tab="OCR结果">
        <div class="ocr-container">
          <div v-if="ocrContent" class="ocr-wrapper">
            <VditorEditor
              :model-value="ocrContent"
              :read-only="true"
              :preview="true"
              height="100%"
              mode="sv"
              placeholder="暂无OCR结果"
            />
          </div>
          <div v-else class="no-ocr">
            <TheIcon icon="material-symbols:text-fields" :size="48" />
            <p>暂无OCR结果</p>
          </div>
        </div>
      </NTabPane>
    </NTabs>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { NTabs, NTabPane, NTag, useMessage } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'
import VditorEditor from '@/components/common/VditorEditor.vue'
import fileApi from '@/api/file'

defineOptions({ name: 'GeneralPagePreviewer' })

const props = defineProps({
  page: {
    type: Object,
    required: true
  },
  editMode: {
    type: Boolean,
    default: false
  }
})

const message = useMessage()
const imageUrl = ref('')
const isImageLoading = ref(false)

// Convert data to markdown format for vditor display
function formatDataForVditor(data) {
  if (!data) return ''
  
  try {
    let content = ''
    
    if (typeof data === 'string') {
      // Try to parse as JSON first
      try {
        const parsed = JSON.parse(data)
        content = '```json\n' + JSON.stringify(parsed, null, 2) + '\n```'
      } catch {
        // If not JSON, treat as plain text
        content = data
      }
    } else {
      // Convert object to formatted JSON
      content = '```json\n' + JSON.stringify(data, null, 2) + '\n```'
    }
    
    return content
  } catch (error) {
    console.error('Error formatting data for vditor:', error)
    return typeof data === 'string' ? data : JSON.stringify(data, null, 2)
  }
}

// Computed properties for formatted content
const ocrContent = computed(() => {
  return formatDataForVditor(props.page.ocr_result)
})

const cvContent = computed(() => {
  return formatDataForVditor(props.page.cv_result)
})

const processContent = computed(() => {
  return formatDataForVditor(props.page.process_result)
})

// 改进图片URL获取和错误处理
async function fetchImageUrl() {
  if (!props.page.id || !props.page.image_url) return

  isImageLoading.value = true
  try {
    const response = await fileApi.getDownloadUrlByPageId(props.page.id)
    if (response?.data) {
      const url = response.data
      if (url && (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('/api/') || url.startsWith('/minio/'))) {
        imageUrl.value = url
      } else {
        throw new Error('Invalid image URL format')
      }
    }
  } catch (error) {
    console.error('Error fetching image URL:', error)
    message.error('获取图像链接失败')
  } finally {
    isImageLoading.value = false
  }
}

// Watch for page changes
watch(() => props.page, async (newPage, oldPage) => {
  if (newPage && newPage.id !== oldPage?.id) {
    await fetchImageUrl()
  }
}, { immediate: true })

// Event handlers
function handleImageLoad(event) {
  console.log('Image loaded:', event.target.naturalWidth, 'x', event.target.naturalHeight)
}

function handleImageError(event) {
  console.error('Image load error:', event)
  message.error('图像加载失败，请检查图像链接是否有效')
}
</script>

<style scoped>
.general-page-previewer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.full-height-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.n-tabs-nav) {
  flex-shrink: 0;
}

:deep(.n-tab-pane) {
  height: 100%;
  overflow: auto;
}

.image-container,
.ocr-container {
  height: 100%;
  padding: 16px 0;
}

.ocr-wrapper {
  height: calc(100% - 32px); /* 减去上下padding */
  width: 100%;
}

.image-wrapper {
  position: relative;
  text-align: center;
}

.page-image {
  max-width: 100%;
  max-height: 70vh;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.no-image,
.no-ocr,
.no-cv,
.no-process {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.no-image p,
.no-ocr p,
.no-cv p,
.no-process p {
  margin-top: 16px;
  font-size: 16px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

<template>
  <div class="general-page-editor">
    <NTabs type="line" animated>
      <!-- Image Tab -->
      <NTabPane name="image" tab="原始图像">
        <div class="image-container">
          <!-- 图像显示部分与 GeneralPagePreviewer 相同 -->
          <div v-if="page.image_url" class="image-wrapper">
            <img
              v-if="!isImageLoading && imageUrl"
              :src="imageUrl"
              :alt="`第${page.page_number}页`"
              class="page-image"
              @load="handleImageLoad"
              @error="handleImageError"
            />
            <div v-else-if="isImageLoading" class="loading-state">
              <TheIcon icon="material-symbols:loading" :size="48" class="loading-icon" />
              <p>加载中...</p>
            </div>
            <div class="image-info">
              <NTag size="small">
                {{ page.image_width }}×{{ page.image_height }}
              </NTag>
              <NTag size="small" v-if="page.image_ppi">
                {{ page.image_ppi }} PPI
              </NTag>
            </div>
          </div>
          <div v-else class="no-image">
            <TheIcon icon="material-symbols:image-not-supported-outline" :size="48" />
            <p>暂无图像</p>
          </div>
        </div>
      </NTabPane>

      <!-- OCR Results Tab -->
      <NTabPane name="ocr" tab="OCR结果">
        <div class="ocr-container">
          <VditorEditor
            v-model:model-value="editableOcrResult"
            :read-only="false"
            :height="500"
            mode="sv"
            placeholder="请输入OCR结果"
          />
        </div>
      </NTabPane>
    </NTabs>

    <!-- 保存按钮 -->
    <div class="action-bar">
      <NSpace>
        <NButton type="primary" @click="handleSave" :loading="saving">
          保存
        </NButton>
      </NSpace>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { NTabs, NTabPane, NTag, NButton, NSpace, useMessage } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'
import VditorEditor from '@/components/common/VditorEditor.vue'
import fileApi from '@/api/file'
import api from '@/api'

defineOptions({ name: 'GeneralPageEditor' })

const props = defineProps({
  page: {
    type: Object,
    required: true
  },
  editMode: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['save'])

const message = useMessage()
const imageUrl = ref('')
const saving = ref(false)
const isImageLoading = ref(false)

// 可编辑的数据
const editableOcrResult = ref('')

// 格式化数据为Vditor可用的格式
function formatDataForVditor(data) {
  if (!data) return ''

  try {
    let content = ''

    if (typeof data === 'string') {
      // 尝试解析为JSON
      try {
        const parsed = JSON.parse(data)
        content = '```json\n' + JSON.stringify(parsed, null, 2) + '\n```'
      } catch {
        // 如果不是JSON，当作纯文本
        content = data
      }
    } else {
      // 将对象转换为格式化的JSON
      content = '```json\n' + JSON.stringify(data, null, 2) + '\n```'
    }

    return content
  } catch (error) {
    console.error('Error formatting data for vditor:', error)
    return typeof data === 'string' ? data : JSON.stringify(data, null, 2)
  }
}

// 初始化可编辑数据
function initializeEditableData() {
  editableOcrResult.value = formatDataForVditor(props.page.ocr_result)
}

// 获取页面图像URL
async function fetchImageUrl() {
  if (!props.page.id || !props.page.image_url) return

  isImageLoading.value = true
  try {
    const response = await fileApi.getDownloadUrlByPageId(props.page.id)
    if (response?.data) {
      const url = response.data
      if (url && (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('/api/') || url.startsWith('/minio/'))) {
        imageUrl.value = url
      } else {
        throw new Error('Invalid image URL format')
      }
    }
  } catch (error) {
    console.error('Error fetching image URL:', error)
    message.error('获取图像链接失败')
  } finally {
    isImageLoading.value = false
  }
}

// 保存编辑结果
async function handleSave() {
  try {
    saving.value = true

    // 提取编辑后的数据
    const ocrResult = editableOcrResult.value

    // 准备更新数据
    const updateData = {
      id: props.page.id,
      ocr_result: ocrResult
    }

    // 调用 API 保存数据 - 参考 EducationPageEditor.vue 的方式
    const response = await api.updatePage(updateData)

    if (response.code === 200) {
      message.success('保存成功')
      emit('save', updateData)
    } else {
      message.error(`保存失败: ${response.msg}`)
    }
  } catch (error) {
    console.error('Error saving page data:', error)
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 监听页面变化
watch(() => props.page, async (newPage, oldPage) => {
  if (newPage && newPage.id !== oldPage?.id) {
    await fetchImageUrl()
    initializeEditableData()
  }
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
  initializeEditableData()
})

// 事件处理函数
function handleImageLoad(event) {
  console.log('Image loaded:', event.target.naturalWidth, 'x', event.target.naturalHeight)
}

function handleImageError(event) {
  console.error('Image load error:', event)
  message.error('图像加载失败，请检查图像链接是否有效')
}
</script>

<style scoped>
.general-page-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.image-container,
.ocr-container {
  padding: 16px 0;
}

.image-wrapper {
  position: relative;
  text-align: center;
}

.page-image {
  max-width: 100%;
  max-height: 70vh;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-info {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.no-image p {
  margin-top: 16px;
  font-size: 16px;
}

.action-bar {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  padding: 0 16px 16px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

<template>
  <div class="education-page-editor">
    <div class="editor-toolbar">
      <NSpace>
        <NButton type="primary" @click="handleSave" :loading="saving">
          <template #icon>
            <TheIcon icon="material-symbols:save" />
          </template>
          保存
        </NButton>
        <NButton @click="handleReset">
          <template #icon>
            <TheIcon icon="material-symbols:refresh" />
          </template>
          重置
        </NButton>
        <NButton @click="addRegion" :disabled="!fabricCanvas">
          <template #icon>
            <TheIcon icon="material-symbols:add-box" />
          </template>
          添加区域
        </NButton>
        <NButton @click="deleteSelectedRegion" :disabled="!selectedRegion">
          <template #icon>
            <TheIcon icon="material-symbols:delete" />
          </template>
          删除选中区域
        </NButton>
      </NSpace>
    </div>

    <NTabs type="line" animated>
      <!-- Interactive Image Editor Tab -->
      <NTabPane name="image" tab="图像编辑">
        <div class="image-container">
          <div v-if="page.image_url" class="image-wrapper">
            <canvas
              ref="canvasRef"
              class="page-canvas"
            />
            <div class="image-info">
              <NTag size="small">
                {{ page.image_width }}×{{ page.image_height }}
              </NTag>
              <NTag size="small" v-if="page.image_ppi">
                {{ page.image_ppi }} PPI
              </NTag>
              <NTag size="small" v-if="editableCvRegions.length">
                {{ editableCvRegions.length }} 个区域
              </NTag>
            </div>

            <!-- Region properties panel -->
            <div v-if="selectedRegion" class="region-properties">
              <NCard title="区域属性" size="small">
                <NForm :model="selectedRegion" label-placement="left" label-width="60">
                  <NFormItem label="X坐标">
                    <NInputNumber
                      v-model:value="selectedRegion.x"
                      :min="0"
                      :max="page.image_width"
                      @update:value="updateSelectedRegion"
                    />
                  </NFormItem>
                  <NFormItem label="Y坐标">
                    <NInputNumber
                      v-model:value="selectedRegion.y"
                      :min="0"
                      :max="page.image_height"
                      @update:value="updateSelectedRegion"
                    />
                  </NFormItem>
                  <NFormItem label="宽度">
                    <NInputNumber
                      v-model:value="selectedRegion.width"
                      :min="1"
                      :max="page.image_width"
                      @update:value="updateSelectedRegion"
                    />
                  </NFormItem>
                  <NFormItem label="高度">
                    <NInputNumber
                      v-model:value="selectedRegion.height"
                      :min="1"
                      :max="page.image_height"
                      @update:value="updateSelectedRegion"
                    />
                  </NFormItem>
                  <NFormItem label="类型">
                    <NInput
                      v-model:value="selectedRegion.type"
                      placeholder="区域类型"
                      @update:value="updateSelectedRegion"
                    />
                  </NFormItem>
                </NForm>
              </NCard>
            </div>
          </div>
          <div v-else class="no-image">
            <TheIcon icon="material-symbols:image-not-supported-outline" :size="48" />
            <p>暂无图像</p>
          </div>
        </div>
      </NTabPane>

      <!-- JSON Editor Tabs -->
      <NTabPane name="content" tab="解析内容">
        <div class="content-editor">
          <NCard title="解析结果编辑" size="small">
            <NInput
              v-model:value="editableProcessResult"
              type="textarea"
              placeholder="请输入解析结果 (JSON格式)"
              :rows="20"
              :autosize="{ minRows: 10, maxRows: 30 }"
            />
          </NCard>
        </div>
      </NTabPane>

      <NTabPane name="ocr" tab="OCR结果">
        <div class="ocr-editor">
          <NCard title="OCR结果编辑" size="small">
            <JsonEditor
              v-model="editableOcrResultJson"
              :read-only="false"
              mode="tree"
              height="500px"
              @change="handleOcrResultChange"
            />
          </NCard>
        </div>
      </NTabPane>

      <NTabPane name="cv" tab="CV结果">
        <div class="cv-editor">
          <NCard title="CV结果编辑" size="small">
            <JsonEditor
              v-model="editableCvResultJson"
              :read-only="false"
              mode="tree"
              height="500px"
              @change="handleCvResultChange"
            />
          </NCard>
        </div>
      </NTabPane>
    </NTabs>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import {
  NTabs, NTabPane, NCard, NTag, NButton, NSpace, NInput, NInputNumber,
  NForm, NFormItem, useMessage
} from 'naive-ui'
import { Canvas, FabricImage, Rect, Text } from 'fabric'
import TheIcon from '@/components/icon/TheIcon.vue'
import JsonEditor from '@/components/common/JsonEditor.vue'
import api from '@/api'
import fileApi from '@/api/file'

defineOptions({ name: 'EducationPageEditor' })

const props = defineProps({
  page: {
    type: Object,
    required: true
  },
  editMode: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['save'])
const message = useMessage()

// Component state
const saving = ref(false)
const canvasRef = ref(null)
const fabricCanvas = ref(null)
const selectedRegion = ref(null)
const editableProcessResult = ref('')
const editableOcrResult = ref('')
const editableCvResult = ref('')
const editableCvRegions = ref([])

// JSON editor reactive variables
const editableOcrResultJson = ref({})
const editableCvResultJson = ref({})

// Initialize editable data
function initializeEditableData() {
  editableProcessResult.value = formatJsonString(props.page.process_result)
  editableOcrResult.value = formatJsonString(props.page.ocr_result)
  editableCvResult.value = formatJsonString(props.page.cv_result)

  // Initialize JSON editor variables
  try {
    editableOcrResultJson.value = typeof props.page.ocr_result === 'string'
      ? JSON.parse(props.page.ocr_result || '{}')
      : props.page.ocr_result || {}
  } catch (error) {
    editableOcrResultJson.value = {}
  }

  try {
    editableCvResultJson.value = typeof props.page.cv_result === 'string'
      ? JSON.parse(props.page.cv_result || '{}')
      : props.page.cv_result || {}
  } catch (error) {
    editableCvResultJson.value = {}
  }

  // Initialize CV regions
  try {
    const cvData = typeof props.page.cv_result === 'string'
      ? JSON.parse(props.page.cv_result)
      : props.page.cv_result

    editableCvRegions.value = cvData?.regions || cvData?.boxes || []
  } catch (error) {
    editableCvRegions.value = []
  }
}

// Helper function to format JSON string
function formatJsonString(data) {
  if (!data) return ''

  try {
    const parsed = typeof data === 'string' ? JSON.parse(data) : data
    return JSON.stringify(parsed, null, 2)
  } catch (error) {
    return typeof data === 'string' ? data : JSON.stringify(data, null, 2)
  }
}

// Validate JSON string
function validateJson(jsonString) {
  if (!jsonString.trim()) return { valid: true, data: null }

  try {
    const parsed = JSON.parse(jsonString)
    return { valid: true, data: parsed }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

// Canvas functions
async function initializeCanvas() {
  if (!canvasRef.value || !props.page.image_url) return

  try {
    // Dispose existing canvas if it exists
    if (fabricCanvas.value) {
      fabricCanvas.value.dispose()
      fabricCanvas.value = null
    }

    // Initialize Fabric.js canvas with editing capabilities
    fabricCanvas.value = new Canvas(canvasRef.value, {
      selection: true,
      interactive: true
    })

    // Load and display the image
    await loadImage()

    // Draw CV regions
    drawCVRegions()

    // Set up event handlers
    setupCanvasEvents()
  } catch (error) {
    console.error('Error initializing canvas:', error)
    message.error('初始化画布失败')
  }
}

async function loadImage() {
  try {
    // Get download URL for the page image
    const response = await fileApi.getDownloadUrlByPageId(props.page.id)
    if (!response || !response.data) {
      throw new Error('Failed to get download URL')
    }

    const imageUrl = response.data

    // 验证URL格式
    if (!imageUrl || !(imageUrl.startsWith('http://') || imageUrl.startsWith('https://') || imageUrl.startsWith('/api/') || imageUrl.startsWith('/minio/'))) {
      throw new Error('Invalid image URL format: ' + imageUrl)
    }

    return new Promise((resolve, reject) => {
      FabricImage.fromURL(imageUrl, {
        crossOrigin: 'anonymous'
      }).then((img) => {
        if (!img) {
          reject(new Error('Failed to load image'))
          return
        }

        // Calculate scale to fit canvas
        const maxWidth = 600
        const maxHeight = 500
        const scale = Math.min(
          maxWidth / img.width,
          maxHeight / img.height,
          1
        )

        img.scale(scale)

        // Set canvas size
        fabricCanvas.value.setDimensions({
          width: img.width * scale,
          height: img.height * scale
        })

        // Set image as background - same as previewer
        fabricCanvas.value.backgroundImage = img

        // Force canvas to render
        fabricCanvas.value.renderAll()

        console.log('Image loaded and added to canvas:', {
          imageSize: { width: img.width, height: img.height },
          canvasSize: { width: fabricCanvas.value.width, height: fabricCanvas.value.height },
          scale: scale
        })

        resolve()
      }).catch((error) => {
        console.error('FabricImage.fromURL error:', error)
        reject(error)
      })
    })
  } catch (error) {
    console.error('Error getting download URL:', error)
    throw error
  }
}

function drawCVRegions() {
  if (!fabricCanvas.value || !editableCvRegions.value.length) return

  // Clear existing regions
  const objects = fabricCanvas.value.getObjects()
  objects.forEach(obj => {
    if (obj.type === 'rect' && obj.isRegion) {
      fabricCanvas.value.remove(obj)
    }
  })

  // Get canvas scale factor
  const canvasWidth = fabricCanvas.value.width
  const canvasHeight = fabricCanvas.value.height
  const imageWidth = props.page.image_width || canvasWidth
  const imageHeight = props.page.image_height || canvasHeight

  const scaleX = canvasWidth / imageWidth
  const scaleY = canvasHeight / imageHeight

  editableCvRegions.value.forEach((region, index) => {
    const x = (region.x || region.left || 0) * scaleX
    const y = (region.y || region.top || 0) * scaleY
    const width = (region.width || region.w || 0) * scaleX
    const height = (region.height || region.h || 0) * scaleY

    // Create editable rectangle
    const rect = new Rect({
      left: x,
      top: y,
      width: width,
      height: height,
      fill: 'rgba(255, 71, 87, 0.2)',
      stroke: '#ff4757',
      strokeWidth: 2,
      selectable: true,
      evented: true,
      isRegion: true,
      regionIndex: index
    })

    fabricCanvas.value.add(rect)
  })

  // Force canvas to render after adding regions
  fabricCanvas.value.renderAll()
}

function setupCanvasEvents() {
  if (!fabricCanvas.value) return

  // Handle object selection
  fabricCanvas.value.on('selection:created', (e) => {
    const activeObject = e.selected[0]
    if (activeObject && activeObject.isRegion) {
      const regionIndex = activeObject.regionIndex
      selectedRegion.value = { ...editableCvRegions.value[regionIndex] }
    }
  })

  fabricCanvas.value.on('selection:updated', (e) => {
    const activeObject = e.selected[0]
    if (activeObject && activeObject.isRegion) {
      const regionIndex = activeObject.regionIndex
      selectedRegion.value = { ...editableCvRegions.value[regionIndex] }
    }
  })

  fabricCanvas.value.on('selection:cleared', () => {
    selectedRegion.value = null
  })

  // Handle object modification
  fabricCanvas.value.on('object:modified', (e) => {
    const activeObject = e.target
    if (activeObject && activeObject.isRegion) {
      updateRegionFromCanvas(activeObject)
    }
  })
}

function updateRegionFromCanvas(canvasObject) {
  const regionIndex = canvasObject.regionIndex
  if (regionIndex >= 0 && regionIndex < editableCvRegions.value.length) {
    // Convert canvas coordinates back to image coordinates
    const canvasWidth = fabricCanvas.value.width
    const canvasHeight = fabricCanvas.value.height
    const imageWidth = props.page.image_width || canvasWidth
    const imageHeight = props.page.image_height || canvasHeight

    const scaleX = imageWidth / canvasWidth
    const scaleY = imageHeight / canvasHeight

    const updatedRegion = {
      ...editableCvRegions.value[regionIndex],
      x: Math.round(canvasObject.left * scaleX),
      y: Math.round(canvasObject.top * scaleY),
      width: Math.round(canvasObject.width * canvasObject.scaleX * scaleX),
      height: Math.round(canvasObject.height * canvasObject.scaleY * scaleY)
    }

    editableCvRegions.value[regionIndex] = updatedRegion

    // Update selected region if it's the same one
    if (selectedRegion.value && selectedRegion.value === editableCvRegions.value[regionIndex]) {
      selectedRegion.value = { ...updatedRegion }
    }

    // Update CV result JSON
    updateCvResultFromRegions()
  }
}

// Region manipulation functions
function addRegion() {
  if (!fabricCanvas.value) return

  const canvasWidth = fabricCanvas.value.width
  const canvasHeight = fabricCanvas.value.height
  const imageWidth = props.page.image_width || canvasWidth
  const imageHeight = props.page.image_height || canvasHeight

  const scaleX = imageWidth / canvasWidth
  const scaleY = imageHeight / canvasHeight

  // Create new region in the center
  const newRegion = {
    x: Math.round((canvasWidth * 0.4) * scaleX),
    y: Math.round((canvasHeight * 0.4) * scaleY),
    width: Math.round((canvasWidth * 0.2) * scaleX),
    height: Math.round((canvasHeight * 0.2) * scaleY),
    type: 'text'
  }

  editableCvRegions.value.push(newRegion)
  drawCVRegions()
  updateCvResultFromRegions()

  message.success('已添加新区域')
}

function deleteSelectedRegion() {
  if (!selectedRegion.value || !fabricCanvas.value) return

  const activeObject = fabricCanvas.value.getActiveObject()
  if (activeObject && activeObject.isRegion) {
    const regionIndex = activeObject.regionIndex

    // Remove from regions array
    editableCvRegions.value.splice(regionIndex, 1)

    // Remove from canvas
    fabricCanvas.value.remove(activeObject)

    // Clear selection
    selectedRegion.value = null

    // Redraw regions to update indices
    drawCVRegions()
    updateCvResultFromRegions()

    message.success('已删除区域')
  }
}

function updateSelectedRegion() {
  if (!selectedRegion.value || !fabricCanvas.value) return

  const activeObject = fabricCanvas.value.getActiveObject()
  if (activeObject && activeObject.isRegion) {
    const regionIndex = activeObject.regionIndex

    // Update the region data
    editableCvRegions.value[regionIndex] = { ...selectedRegion.value }

    // Update canvas object
    const canvasWidth = fabricCanvas.value.width
    const canvasHeight = fabricCanvas.value.height
    const imageWidth = props.page.image_width || canvasWidth
    const imageHeight = props.page.image_height || canvasHeight

    const scaleX = canvasWidth / imageWidth
    const scaleY = canvasHeight / imageHeight

    activeObject.set({
      left: selectedRegion.value.x * scaleX,
      top: selectedRegion.value.y * scaleY,
      width: selectedRegion.value.width * scaleX,
      height: selectedRegion.value.height * scaleY,
      scaleX: 1,
      scaleY: 1
    })

    fabricCanvas.value.renderAll()
    updateCvResultFromRegions()
  }
}

function updateCvResultFromRegions() {
  try {
    const cvData = {
      regions: editableCvRegions.value
    }
    editableCvResult.value = JSON.stringify(cvData, null, 2)
    editableCvResultJson.value = cvData
  } catch (error) {
    console.error('Error updating CV result:', error)
  }
}

// JSON Editor change handlers
function handleOcrResultChange(newValue) {
  try {
    editableOcrResult.value = JSON.stringify(newValue, null, 2)
  } catch (error) {
    console.error('Error updating OCR result string:', error)
  }
}

function handleCvResultChange(newValue) {
  try {
    editableCvResult.value = JSON.stringify(newValue, null, 2)

    // Update CV regions if the structure contains regions
    if (newValue && newValue.regions && Array.isArray(newValue.regions)) {
      editableCvRegions.value = newValue.regions
      if (fabricCanvas.value) {
        drawCVRegions()
      }
    }
  } catch (error) {
    console.error('Error updating CV result string:', error)
  }
}

// Event handlers
async function handleSave() {
  try {
    saving.value = true

    // Validate all JSON fields
    const processValidation = validateJson(editableProcessResult.value)
    const ocrValidation = validateJson(editableOcrResult.value)
    const cvValidation = validateJson(editableCvResult.value)

    if (!processValidation.valid) {
      message.error(`解析结果格式错误: ${processValidation.error}`)
      return
    }

    if (!ocrValidation.valid) {
      message.error(`OCR结果格式错误: ${ocrValidation.error}`)
      return
    }

    if (!cvValidation.valid) {
      message.error(`CV结果格式错误: ${cvValidation.error}`)
      return
    }

    // Prepare update data
    const updateData = {
      id: props.page.id,
      process_result: processValidation.data,
      ocr_result: ocrValidation.data,
      cv_result: cvValidation.data
    }

    // Call API to update page
    const response = await api.updatePage(updateData)

    if (response.code === 200) {
      message.success('保存成功')
      emit('save', updateData)
    } else {
      message.error(`保存失败: ${response.msg}`)
    }
  } catch (error) {
    console.error('Error saving page:', error)
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

function handleReset() {
  initializeEditableData()
  selectedRegion.value = null
  if (fabricCanvas.value) {
    drawCVRegions()
  }
  message.info('已重置为原始数据')
}

// Watch for page changes
watch(() => props.page, async (newPage, oldPage) => {
  if (newPage && newPage.id !== oldPage?.id) {
    console.log('Page changed in editor, reinitializing:', newPage.id)
    initializeEditableData()
    await nextTick()
    await initializeCanvas()
  }
}, { immediate: true })

// Lifecycle
onMounted(async () => {
  await nextTick()
  await initializeCanvas()
})

onUnmounted(() => {
  if (fabricCanvas.value) {
    fabricCanvas.value.dispose()
    fabricCanvas.value = null
  }
})
</script>

<style scoped>
.education-page-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  padding: 16px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 16px;
}

.image-container {
  padding: 16px 0;
  display: flex;
  gap: 16px;
}

.image-wrapper {
  flex: 1;
  text-align: center;
}

.page-canvas {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: crosshair;
}

.image-info {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.region-properties {
  width: 300px;
  flex-shrink: 0;
}

.no-image {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.no-image p {
  margin: 16px 0 0 0;
}

.content-editor,
.ocr-editor,
.cv-editor {
  padding: 16px 0;
}
</style>

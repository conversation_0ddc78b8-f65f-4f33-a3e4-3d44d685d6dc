<script setup>
/**
 * FileSelector Component
 *
 * A reusable component for selecting files from a tree structure.
 * Features:
 * - File tree display with checkboxes
 * - Directory expansion/collapse
 * - File search functionality
 * - Cascading selection (checking a directory checks all children)
 */
import { ref, computed, watch } from 'vue'
import { NTree, NInput, NButton, NSpace, NModal, NIcon, NEmpty, NSpin, useMessage } from 'naive-ui'
import TheIcon from '@/components/icon/TheIcon.vue'
import fileApi from '@/api/file'

// Props definition with detailed documentation
const props = defineProps({
  /** Array of initially selected file IDs */
  selectedFiles: {
    type: Array,
    default: () => []
  },
  /** Root directory ID to start from (null means root) */
  rootdir: {
    type: [Number, null],
    default: null
  },
  /** Modal title */
  title: {
    type: String,
    default: '选择文件'
  },
  /** Maximum number of files to load per request */
  pageSize: {
    type: Number,
    default: 100
  }
})

// Events that this component can emit
const emit = defineEmits([
  /** Emitted when selection is confirmed */
  'confirm',
  /** Emitted when selection is canceled */
  'cancel'
])

// Setup message service for notifications
const message = useMessage()

// UI state
const visible = ref(false)
const loading = ref(false)
const searchValue = ref('')

// Tree state
const treeData = ref([])
const checkedKeys = ref([])
const expandedKeys = ref([])

/**
 * Filters tree data based on search value
 * Keeps parent nodes if any of their children match the search
 */
const filteredTreeData = computed(() => {
  if (!searchValue.value) return treeData.value

  const filterTree = (nodes) => {
    if (!nodes) return []

    return nodes.filter(node => {
      // Check if current node matches
      const matchesSearch = node.name.toLowerCase().includes(searchValue.value.toLowerCase())

      // Recursively filter children
      if (node.children && node.children.length) {
        const filteredChildren = filterTree(node.children)
        node.children = filteredChildren

        // Include this node if it has matching children
        return matchesSearch || filteredChildren.length > 0
      }

      return matchesSearch
    })
  }

  // Create a deep copy to avoid modifying the original data
  return filterTree(JSON.parse(JSON.stringify(treeData.value)))
})

/**
 * Opens the file selector modal and loads files
 */
function open() {
  visible.value = true
  checkedKeys.value = [...props.selectedFiles]
  expandedKeys.value = []
  loadFiles()
}

/**
 * Closes the file selector modal
 */
function close() {
  visible.value = false
}

/**
 * Handles cancel button click
 * Closes the modal and emits cancel event
 */
function handleCancel() {
  close()
  emit('cancel')
}

/**
 * Handles confirm button click
 * Closes the modal and emits confirm event with selected file IDs
 */
function handleConfirm() {
  close()
  emit('confirm', {
    modified: true,
    selectedIds: checkedKeys.value
  })
}

/**
 * Recursively finds a node by ID in the tree data
 * @param {Array} nodes - Array of tree nodes to search
 * @param {Number} id - ID of the node to find
 * @returns {Object|null} - Found node or null
 */
function findNodeById(nodes, id) {
  if (!nodes || nodes.length === 0) return null

  for (const node of nodes) {
    if (node.id === id || node.key === id) {
      return node
    }

    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id)
      if (found) return found
    }
  }

  return null
}

/**
 * Transforms file data from API to tree node format
 * @param {Object} file - File data from API
 * @returns {Object} - Tree node object
 */
function transformFileToNode(file) {
  return {
    id: file.id,
    key: file.id,
    name: file.name,
    isLeaf: !file.is_directory,
    is_directory: file.is_directory,
    children: file.is_directory ? [] : undefined,
    // Add additional properties for display
    original_name: file.original_name,
    mime_type: file.mime_type,
    size_bytes: file.size_bytes,
    parent_id: file.parent_id
  }
}

/**
 * Loads files from the API
 * @param {Number|null} parentId - Parent directory ID (null for root)
 */
async function loadFiles(parentId = props.rootdir) {
  try {
    loading.value = true
    const { data } = await fileApi.getFiles({
      parent_id: parentId,
      page_size: props.pageSize
    })

    // Transform data into tree format
    const transformedData = data.map(transformFileToNode)
    treeData.value = transformedData

    // Preload directory contents in the background
    await preloadDirectoryContents(transformedData)
  } catch (error) {
    console.error('Error loading files:', error)
    message.error('加载文件失败')
  } finally {
    loading.value = false
  }
}

/**
 * Preloads contents of directories in the background
 * @param {Array} nodes - Array of directory nodes
 */
async function preloadDirectoryContents(nodes) {
  const directoryNodes = nodes.filter(item => item.is_directory)
  if (directoryNodes.length > 0 && directoryNodes.length < 5) {
    for (const dirNode of directoryNodes) {
      await loadDirectoryContents(dirNode)
    }
  }
}

/**
 * Loads contents of a directory
 * @param {Object} node - Directory node
 */
async function loadDirectoryContents(node) {
  if (!node || !node.is_directory) return

  try {
    // Only load if this is a directory and it doesn't have loaded children yet
    if (!node.children || node.children.length === 0) {
      const { data } = await fileApi.getFiles({
        parent_id: node.id,
        page_size: props.pageSize
      })

      // Transform data into tree format
      const children = data.map(transformFileToNode)

      // Update the node's children and trigger reactivity
      node.children = children

      // Force reactivity by updating the entire tree data
      treeData.value = [...treeData.value]
    }
  } catch (error) {
    console.error('Error loading directory contents for node:', node.id, error)
    // Don't show error message for background loading to avoid UI noise
  }
}

/**
 * Handles expanded keys update
 * Loads children for newly expanded directories
 * @param {Array} keys - New expanded keys
 */
function handleExpandedKeysUpdate(keys) {
  if (!Array.isArray(keys)) {
    console.warn('Expanded keys is not an array:', keys)
    return
  }

  // Find newly expanded keys
  const newlyExpanded = keys.filter(key => !expandedKeys.value.includes(key))

  // Update expanded keys
  expandedKeys.value = keys

  // Load children for newly expanded directories
  if (newlyExpanded.length > 0) {
    loadNewlyExpandedDirectories(newlyExpanded)
  }
}

/**
 * Loads contents of newly expanded directories
 * @param {Array} newlyExpanded - Array of newly expanded directory IDs
 */
async function loadNewlyExpandedDirectories(newlyExpanded) {
  // Process one directory at a time to avoid UI freezing
  for (const key of newlyExpanded) {
    const node = findNodeById(treeData.value, key)

    if (node && node.is_directory) {
      // Only show loading for the first directory to avoid flickering
      if (newlyExpanded.indexOf(key) === 0) {
        loading.value = true
      }
      try {
        await loadDirectoryContents(node)
      } finally {
        // Only hide loading after the last directory
        if (newlyExpanded.indexOf(key) === newlyExpanded.length - 1) {
          loading.value = false
        }
      }
    }
  }
}

/**
 * Handles checked keys update
 * @param {Array} keys - New checked keys
 */
function handleCheckedKeysUpdate(keys) {
  // With the cascade property enabled on NTree, we can simply update the checked keys
  // The tree component will handle the cascading behavior automatically
  checkedKeys.value = keys
}

// Watch for changes in selectedFiles prop
watch(() => props.selectedFiles, (newVal) => {
  if (visible.value) {
    checkedKeys.value = [...newVal]
  }
})

// Expose methods to parent component
defineExpose({
  open,
  close
})
</script>

<template>
  <NModal
    v-model:show="visible"
    :title="title"
    preset="card"
    style="width: 650px; max-width: 90vw"
  >
    <NSpin :show="loading">
      <div class="file-selector">
        <!-- Search input -->
        <div class="search-container">
          <NInput
            v-model:value="searchValue"
            placeholder="搜索文件或目录"
            clearable
          >
            <template #prefix>
              <NIcon>
                <TheIcon icon="mdi:magnify" :size="18" />
              </NIcon>
            </template>
          </NInput>
        </div>

        <!-- File tree -->
        <div class="tree-container">
          <NTree
            v-if="filteredTreeData.length > 0"
            :data="filteredTreeData"
            checkable
            cascade
            selectable
            :checked-keys="checkedKeys"
            :expanded-keys="expandedKeys"
            key-field="id"
            label-field="name"
            children-field="children"
            remote
            :default-expand-all="false"
            @update:checked-keys="handleCheckedKeysUpdate"
            @update:expanded-keys="handleExpandedKeysUpdate"
          />
          <NEmpty v-else description="没有找到文件" />
        </div>

        <!-- Action buttons -->
        <div class="action-buttons">
          <NSpace>
            <NButton @click="handleCancel">取消</NButton>
            <NButton type="primary" @click="handleConfirm">确认</NButton>
          </NSpace>
        </div>
      </div>
    </NSpin>
  </NModal>
</template>

<style scoped>
.file-selector {
  display: flex;
  flex-direction: column;
}

.search-container {
  margin-bottom: 16px;
}

.tree-container {
  flex: 1;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 8px;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>

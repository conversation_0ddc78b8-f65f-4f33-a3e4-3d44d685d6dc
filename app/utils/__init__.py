import os

def singleton(cls):
    """
    Singleton decorator.
    Makes a class a Singleton class (only one instance)
    """
    instances = {}

    def wrapper(*args, **kwargs):
        key = (cls, os.getpid())
        if key not in instances:
            instances[key] = cls(*args, **kwargs)
        return instances[key]
    return wrapper

# Import and expose the MinioClient class
from app.utils.minio_client import MinioClient
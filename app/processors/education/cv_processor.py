"""
Education CV processor for page processing.
"""
from __future__ import annotations
from typing import Dict, List, Any, Optional, Tuple, Union
import asyncio
import json
import os
from datetime import datetime

# Third-party imports
import cv2
import numpy as np

# Application imports
from app.log import logger
from app.processors.base import BaseProcessor

# Type aliases
JSONDict = Dict[str, Any]
Region = Tuple[int, int, int, int, float, float, float, str]  # x, y, w, h, area, aspect_ratio, pixel_density, region_type


class EducationCvProcessor(BaseProcessor):
    """
    CV processor for education bundle type.
    """
    # Default configuration for object detection
    DEFAULT_CONFIG = {
        'min_area': 1000,
        'max_area_ratio': 0.5,
        'aspect_ratio_range': (0.3, 3.5),
        'pixel_density_range': (0.1, 0.9),
        'overlap_threshold': 0.02,
        'debug': False,
        'log_dir': 'logs'  # Default log directory for debug images
    }

    # Region type colors for visualization
    REGION_COLORS = {
        "image": (0, 0, 255),  # Red for images
        "table": (255, 0, 0)   # Blue for tables
    }

    async def process(self, bucket: str, object_key: str, **kwargs) -> Union[Dict[str, Any], str, None]:
        """
        Detect objects in an image stored in MinIO.

        Args:
            bucket: MinIO bucket name
            object_key: Object key in the bucket
            **kwargs: Additional parameters including config

        Returns:
            JSON string with object detection results

        Raises:
            Exception: If CV processing fails at any stage
        """
        try:
            # Extract parameters
            config = kwargs.get('config')

            # Prepare configuration
            config = self._prepare_config(config)
            debug = config.get('debug', False)

            # Log operation
            logger.debug(f"Detecting objects from MinIO: {bucket}/{object_key} (debug={debug})")

            # Get image data from MinIO using base class utility
            image_data = await self._get_image_from_minio(bucket, object_key)

            # Detect objects in the image data
            result = await self._detect_objects(
                image_data=image_data,
                config=config
            )

            # Return only the parsed result without additional metadata
            parsed_result = {
                "objects": result.get("objects", []),
                "object_counts": result.get("object_counts", {}),
                "processing_time_ms": result.get("processing_time_ms", 0),
                "timestamp": result.get("timestamp", "")
            }

            # TODO: should I return a dictory or just the raw json string?
            return json.dumps(parsed_result, ensure_ascii=False, indent=4)

        except Exception as e:
            logger.error(f"Error detecting objects from MinIO: {e}")
            raise

    def _prepare_config(self, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Prepare configuration by merging with defaults.

        Args:
            config: User-provided configuration

        Returns:
            Merged configuration dictionary
        """
        if config is None:
            return self.DEFAULT_CONFIG.copy()

        merged_config = self.DEFAULT_CONFIG.copy()
        merged_config.update(config)
        return merged_config

    async def _detect_objects(self, image_data: bytes, config: Optional[Dict[str, Any]] = None) -> JSONDict:
        """
        Detect objects in an image.

        Args:
            image_data: Raw image data as bytes
            config: Configuration for object detection

        Returns:
            Dict with object detection results

        Raises:
            Exception: Re-raises any exception after logging
        """
        try:
            # Prepare configuration
            config = self._prepare_config(config)
            debug = config.get('debug', False)

            if debug:
                logger.info(f"Debug mode enabled, saving intermediate files to {config.get('log_dir', 'logs')}")

            # Convert bytes to numpy array
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img is None:
                error_msg = "Failed to decode image for object detection"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Get image dimensions
            height, width = img.shape[:2]

            # Process the image (CPU-intensive operation)
            # Run in a separate thread to avoid blocking the event loop
            result = await asyncio.to_thread(
                self._process_image,
                img=img,
                config=config
            )

            # Add image dimensions to result
            result["image_size"] = {
                "width": width,
                "height": height
            }

            return result

        except Exception as e:
            logger.error(f"Error detecting objects: {e}")
            raise

    def _ensure_log_dir(self, config: Dict[str, Any]) -> None:
        """
        Ensure log directory exists.

        Args:
            config: Configuration dictionary
        """
        debug = config.get('debug', False)
        log_dir = config.get('log_dir', 'logs')

        if debug and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            logger.info(f"Created log directory: {log_dir}")

    def _save_debug_image(self, image: np.ndarray, filename: str, config: Dict[str, Any]) -> None:
        """
        Save image for debugging if debug mode is enabled.

        Args:
            image: Image to save
            filename: Filename to save as
            config: Configuration dictionary
        """
        if not config.get('debug', False):
            return

        log_dir = config.get('log_dir', 'logs')
        filepath = os.path.join(log_dir, filename)
        cv2.imwrite(filepath, image)
        logger.debug(f"Saved debug image: {filepath}")

    def _visualize_regions(
        self,
        image: np.ndarray,
        regions: List[Region],
        filename: str,
        config: Dict[str, Any]
    ) -> None:
        """
        Visualize regions on an image and save for debugging.

        Args:
            image: Base image
            regions: List of regions to visualize
            filename: Filename to save as
            config: Configuration dictionary
        """
        if not config.get('debug', False):
            return

        marked_img = image.copy()

        for i, region in enumerate(regions):
            x, y, w, h = region[:4]
            region_type = region[7]
            color = self.REGION_COLORS.get(region_type, (0, 255, 0))  # Default to green
            cv2.rectangle(marked_img, (x, y), (x+w, y+h), color, 2)
            label = f"#{i+1} ({region_type})"
            cv2.putText(marked_img, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

        self._save_debug_image(marked_img, filename, config)

    def _save_region_images(
        self,
        image: np.ndarray,
        regions: List[Region],
        config: Dict[str, Any]
    ) -> None:
        """
        Extract and save individual region images.

        Args:
            image: Source image
            regions: List of regions to extract
            config: Configuration dictionary
        """
        if not config.get('debug', False):
            return

        log_dir = config.get('log_dir', 'logs')

        for i, region in enumerate(regions):
            x, y, w, h = region[:4]
            area = region[4]
            region_type = region[7]
            region_img = image[y:y+h, x:x+w]
            region_filename = f"{region_type}_{i+1}.png"
            cv2.imwrite(os.path.join(log_dir, region_filename), region_img)
            logger.debug(f"Saved {region_type} #{i+1}: pos=({x},{y}), size={w}x{h}, area={area:.1f}")

    def _save_json_data(self, data: Dict[str, Any], config: Dict[str, Any]) -> None:
        """
        Save JSON data for debugging.

        Args:
            data: Data to save as JSON
            config: Configuration dictionary
        """
        if not config.get('debug', False):
            return

        log_dir = config.get('log_dir', 'logs')
        json_path = os.path.join(log_dir, "extraction_info.json")

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        logger.debug(f"Saved JSON data to {json_path}")

    def _process_image(self, img: np.ndarray, config: Dict[str, Any]) -> JSONDict:
        """
        Process an image to detect objects.

        Args:
            img: OpenCV image
            config: Configuration for object detection

        Returns:
            Dict with object detection results
        """
        # Ensure log directory exists
        self._ensure_log_dir(config)

        # Get image dimensions
        height, width = img.shape[:2]

        # Identify regions
        contours, binary, gray = self._identify_regions(img, config)

        # Filter regions
        valid_regions = self._filter_regions(contours, binary, gray, config)

        # Visualize filtered regions
        self._visualize_regions(img, valid_regions, "09_filtered_regions.png", config)

        # Merge overlapping regions
        merged_regions = self._merge_overlapping_regions(valid_regions, config.get('overlap_threshold', 0.1))

        # Sort regions by area (largest first)
        merged_regions.sort(key=lambda r: r[4], reverse=True)

        # Visualize merged regions
        self._visualize_regions(img, merged_regions, "10_merged_regions.png", config)

        # Save individual region images
        self._save_region_images(img, merged_regions, config)

        # Convert regions to JSON format
        objects = []
        for i, (x, y, w, h, area, aspect_ratio, pixel_density, region_type) in enumerate(merged_regions):
            object_info = {
                "id": i + 1,
                "type": region_type,
                "bbox": [int(x), int(y), int(w), int(h)],
                "position": {"x": int(x), "y": int(y), "w": int(w), "h": int(h)},
                "area": float(area),
                "wh_ratio": float(aspect_ratio),
                "density": float(pixel_density)
            }
            objects.append(object_info)

        # Count object types
        images_count = sum(1 for r in merged_regions if r[7] == "image")
        tables_count = sum(1 for r in merged_regions if r[7] == "table")

        # Prepare result data
        result_data = {
            "objects": objects,
            "object_counts": {
                "total": len(objects),
                "images": images_count,
                "tables": tables_count
            },
            "processing_time_ms": 800,  # Simulated processing time
            "timestamp": datetime.now().isoformat()
        }

        # Save JSON data if debug is enabled
        if config.get('debug', False):
            json_data = {
                "image_size": {"width": width, "height": height},
                "process_time": datetime.now().isoformat(),
                "identified_objects": len(merged_regions),
                "objects": objects,
                "object_counts": result_data["object_counts"]
            }
            self._save_json_data(json_data, config)

        return result_data

    def _identify_regions(self, image: np.ndarray, config: Dict[str, Any]) -> Tuple[List, np.ndarray, np.ndarray]:
        """
        Identify regions in an image.

        Args:
            image: OpenCV image
            config: Configuration for region identification

        Returns:
            Tuple of (contours, binary image, gray image)
        """
        # Save original image if debug is enabled
        self._save_debug_image(image, "00_original.png", config)

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        self._save_debug_image(gray, "01_gray.png", config)

        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        self._save_debug_image(blurred, "02_blurred.png", config)

        # Use adaptive threshold for text and tables
        adaptive_thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV, 11, 2
        )
        self._save_debug_image(adaptive_thresh, "03_adaptive_threshold.png", config)

        # Use Otsu's threshold for high-contrast images
        _, otsu_thresh = cv2.threshold(
            blurred, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU
        )
        self._save_debug_image(otsu_thresh, "04_otsu_threshold.png", config)

        # Use Canny edge detection for shapes and lines
        canny_edges = cv2.Canny(blurred, 50, 150)
        self._save_debug_image(canny_edges, "05_canny_edges.png", config)

        # Dilate edges to connect broken lines
        dilated_edges = cv2.dilate(
            canny_edges, np.ones((3, 3), np.uint8), iterations=1
        )
        self._save_debug_image(dilated_edges, "06_dilated_edges.png", config)

        # Combine methods
        combined = cv2.bitwise_or(adaptive_thresh, dilated_edges)
        self._save_debug_image(combined, "07_combined.png", config)

        # Apply morphological operations
        kernel = np.ones((3, 3), np.uint8)
        closed = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
        opened = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel)
        binary = cv2.dilate(opened, kernel, iterations=1)
        self._save_debug_image(binary, "08_morphology.png", config)

        # Find contours
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        logger.debug(f"Detected {len(contours)} contours")

        return contours, binary, gray

    def _filter_regions(
        self,
        contours: List,
        binary_image: np.ndarray,
        gray_image: np.ndarray,
        config: Dict[str, Any]
    ) -> List[Region]:
        """
        Filter contours based on various criteria.

        Args:
            contours: List of contours
            binary_image: Binary image
            gray_image: Grayscale image
            config: Configuration for filtering

        Returns:
            List of valid regions
        """
        min_area = config.get('min_area', 1000)
        max_area_ratio = config.get('max_area_ratio', 0.5)
        aspect_ratio_range = config.get('aspect_ratio_range', (0.1, 10))
        pixel_density_range = config.get('pixel_density_range', (0.1, 0.9))

        # Calculate maximum area limit
        max_area = binary_image.shape[0] * binary_image.shape[1] * max_area_ratio

        # Store valid regions
        valid_regions = []
        filtered_count = 0

        for contour in contours:
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)

            # Calculate area
            rect_area = w * h
            contour_area = cv2.contourArea(contour)

            # Filter by area
            if rect_area < min_area or rect_area > max_area:
                filtered_count += 1
                continue

            # Calculate aspect ratio
            aspect_ratio = float(w) / h if h > 0 else 0

            # Check aspect ratio
            if aspect_ratio < aspect_ratio_range[0] or aspect_ratio > aspect_ratio_range[1]:
                filtered_count += 1
                continue

            # Extract ROIs
            roi_binary = binary_image[y:y+h, x:x+w]
            roi_gray = gray_image[y:y+h, x:x+w]

            if roi_binary.size == 0 or roi_gray.size == 0:
                filtered_count += 1
                continue

            # Calculate pixel density
            pixel_density = np.sum(roi_binary == 255) / roi_binary.size

            # Filter by pixel density
            if pixel_density < pixel_density_range[0] or pixel_density > pixel_density_range[1]:
                filtered_count += 1
                continue

            # Calculate shape complexity
            perimeter = cv2.arcLength(contour, True)
            complexity = perimeter * perimeter / (4 * np.pi * contour_area) if contour_area > 0 else 0

            # Calculate projections for table detection
            horizontal_projection = np.sum(roi_binary, axis=1)
            vertical_projection = np.sum(roi_binary, axis=0)

            h_var = np.var(horizontal_projection) if len(horizontal_projection) > 0 else 0
            v_var = np.var(vertical_projection) if len(vertical_projection) > 0 else 0

            h_ratio = h_var / np.mean(horizontal_projection) if np.mean(horizontal_projection) > 0 else 0
            v_ratio = v_var / np.mean(vertical_projection) if np.mean(vertical_projection) > 0 else 0

            # Determine region type
            region_type = "table" if (complexity > 1.5 and (h_ratio > 0.8 or v_ratio > 0.8)) else "image"

            # Add to valid regions
            valid_regions.append((x, y, w, h, rect_area, aspect_ratio, pixel_density, region_type))

        logger.debug(f"Filtered {filtered_count} regions, kept {len(valid_regions)} valid regions")
        return valid_regions

    def _merge_overlapping_regions(self, regions: List[Region], overlap_threshold: float = 0.1) -> List[Region]:
        """
        Merge overlapping regions.

        Args:
            regions: List of regions
            overlap_threshold: Threshold for merging

        Returns:
            List of merged regions
        """
        if not regions:
            return []

        merged_regions = regions.copy()
        merge_count = 0

        # Continue merging until no more merges are possible
        while True:
            # Find the first pair of overlapping regions
            merge_found = False

            for i in range(len(merged_regions)):
                if merge_found:
                    break

                for j in range(i + 1, len(merged_regions)):
                    # Calculate overlap
                    x1, y1, w1, h1 = merged_regions[i][:4]
                    x2, y2, w2, h2 = merged_regions[j][:4]

                    # Calculate intersection area
                    overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                    overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                    overlap_area = overlap_x * overlap_y

                    # Calculate region areas
                    area1 = w1 * h1
                    area2 = w2 * h2

                    # Calculate overlap ratio relative to the smaller region
                    smaller_area = min(area1, area2)
                    overlap_ratio = overlap_area / smaller_area if smaller_area > 0 else 0

                    # If overlap ratio exceeds threshold, merge the regions
                    if overlap_ratio > overlap_threshold:
                        # Calculate merged region coordinates
                        new_x = min(x1, x2)
                        new_y = min(y1, y2)
                        new_w = max(x1 + w1, x2 + w2) - new_x
                        new_h = max(y1 + h1, y2 + h2) - new_y
                        new_area = new_w * new_h
                        new_aspect_ratio = float(new_w) / new_h if new_h > 0 else 0

                        # Calculate weighted average pixel density
                        new_pixel_density = (merged_regions[i][6] * area1 + merged_regions[j][6] * area2) / (area1 + area2)

                        # Determine region type (prefer table)
                        new_region_type = "table" if merged_regions[i][7] == "table" or merged_regions[j][7] == "table" else "image"

                        # Create merged region
                        merged_region = (new_x, new_y, new_w, new_h, new_area, new_aspect_ratio, new_pixel_density, new_region_type)

                        # Replace first region with merged region and remove second region
                        merged_regions[i] = merged_region
                        merged_regions.pop(j)

                        merge_count += 1
                        merge_found = True
                        break

            # If no merges were found in this iteration, we're done
            if not merge_found:
                break

        logger.info(f"Merged {merge_count} overlapping regions, resulting in {len(merged_regions)} regions")
        return merged_regions

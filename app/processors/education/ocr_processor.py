"""
Education OCR processor for page processing using vision LLM.
"""
from __future__ import annotations
from typing import Dict, Any, Union
import base64
import json

# Application imports
from app.log import logger
from app.ai.llms.llm import get_llm_by_type
from app.ai.prompts.template import get_prompt_template
from app.processors.base import BaseProcessor

# Type aliases
JSONDict = Dict[str, Any]


class EducationOcrProcessor(BaseProcessor):
    """
    OCR processor for education bundle type using LLM vision models.
    """
    # OCR with answer analysis prompt template name
    OCR_WITH_ANSWER_TEMPLATE = "ocr_with_answer"

    # OCR without answer analysis prompt template name
    OCR_WITHOUT_ANSWER_TEMPLATE = "ocr_without_answer"

    async def process(self, bucket: str, object_key: str, **kwargs) -> Union[Dict[str, Any], str, None]:
        """
        Process OCR on an image stored in MinIO using vision LLM.

        Args:
            bucket: MinIO bucket name
            object_key: Object key in the bucket
            **kwargs: Additional parameters including use_answer_analysis

        Returns:
            Parsed JSON result from OCR analysis

        Raises:
            Exception: If OCR processing fails at any stage
        """
        try:
            # Extract parameters
            use_answer_analysis = kwargs.get('use_answer_analysis', True)

            # Get image data from MinIO using base class utility
            image_data = await self._get_image_from_minio(bucket, object_key)

            # Perform OCR on the image data using vision LLM
            logger.debug(f"Starting OCR processing with use_answer_analysis={use_answer_analysis}")
            parsed_result = await self._perform_ocr_with_vision_llm(
                image_data=image_data,
                use_answer_analysis=use_answer_analysis
            )

            logger.debug("OCR processing completed")
            return parsed_result

        except Exception as e:
            logger.error(f"Error performing OCR from MinIO: {e}")
            raise

    async def _perform_ocr_with_vision_llm(
        self,
        image_data: bytes,
        use_answer_analysis: bool = False
    ) -> JSONDict:
        """
        Internal method to perform OCR on an image using vision LLM with structured analysis.

        Args:
            image_data: Raw image data as bytes
            use_answer_analysis: Whether to use the answer analysis prompt template

        Returns:
            Parsed JSON result from OCR

        Raises:
            Exception: If OCR processing fails
        """
        try:
            # Get vision LLM
            vision_llm = get_llm_by_type("vision")

            # Convert image to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')

            # Determine MIME type based on image header
            mime_type = self._determine_mime_type(image_data)

            # Get prompt template
            if use_answer_analysis:
                prompt_content = get_prompt_template(self.OCR_WITH_ANSWER_TEMPLATE)
            else:
                prompt_content = get_prompt_template(self.OCR_WITHOUT_ANSWER_TEMPLATE)

            # Prepare messages for vision LLM
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt_content
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"{mime_type},{base64_image}"
                            }
                        }
                    ]
                }
            ]

            # Call vision LLM
            logger.debug("Calling vision LLM for OCR analysis...")
            response = await vision_llm.ainvoke(messages)

            # Extract JSON from response (handles ```json ... ``` formatting)
            cleaned_json = self._extract_json_from_response(response.content)

            # if cleaned_json is not valid json, return it as pure string
            try:
                parsed_result = json.loads(cleaned_json)
                # TODO: should I return a dictory or just the raw json string?
                parsed_result = json.dumps(parsed_result, ensure_ascii=False, indent=4)
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON response: {e}")
                parsed_result = cleaned_json

            return parsed_result

        except Exception as e:
            logger.error(f"Error performing OCR with vision LLM: {e}")
            raise



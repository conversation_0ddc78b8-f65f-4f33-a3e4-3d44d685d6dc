"""
Processor factory for creating bundle-type specific processors.
"""
from typing import Type, Dict
from app.models.enums import BundleType
from app.log import logger

from .base import BaseProcessor
from .education.ocr_processor import EducationOcrProcessor
from .education.cv_processor import EducationCvProcessor
from .general.ocr_processor import GeneralOcrProcessor
from .general.cv_processor import GeneralCvProcessor


class ProcessorFactory:
    """
    Factory class for creating processors based on bundle type and processing type.
    """
    
    # Mapping of bundle_type and processor_type to processor classes
    _PROCESSOR_MAP: Dict[str, Dict[str, Type[BaseProcessor]]] = {
        BundleType.EDUCATION: {
            "ocr": EducationOcrProcessor,
            "cv": EducationCvProcessor,
        },
        BundleType.GENERAL: {
            "ocr": GeneralOcrProcessor,
            "cv": GeneralCvProcessor,
        },
        # Future bundle types can be added here
        # BundleType.CONTRACT: {
        #     "ocr": ContractOcrProcessor,
        #     "cv": ContractCvProcessor,
        # },
        # BundleType.INVOICE: {
        #     "ocr": InvoiceOcrProcessor,
        #     "cv": InvoiceCvProcessor,
        # },
    }
    
    @classmethod
    def get_processor(cls, bundle_type: BundleType, processor_type: str) -> BaseProcessor:
        """
        Get a processor instance based on bundle type and processor type.
        
        Args:
            bundle_type: The type of bundle (education, general, etc.)
            processor_type: The type of processor (ocr, cv)
            
        Returns:
            Processor instance
            
        Raises:
            ValueError: If bundle_type or processor_type is not supported
        """
        try:
            # Get processor class from mapping
            bundle_processors = cls._PROCESSOR_MAP.get(bundle_type)
            if not bundle_processors:
                logger.warning(f"Unsupported bundle type: {bundle_type}, will use general processor")
                bundle_processors = cls._PROCESSOR_MAP.get(BundleType.GENERAL)
            
            processor_class = bundle_processors.get(processor_type)
            if not processor_class:
                raise ValueError(f"Unsupported processor type '{processor_type}' for bundle type '{bundle_type}'")
            
            # Create and return processor instance
            processor = processor_class()
            return processor
            
        except Exception as e:
            logger.error(f"Error creating processor for {bundle_type}/{processor_type}: {e}")
            raise
    
    @classmethod
    def get_supported_bundle_types(cls) -> list[BundleType]:
        """
        Get list of supported bundle types.
        
        Returns:
            List of supported bundle types
        """
        return list(cls._PROCESSOR_MAP.keys())
    
    @classmethod
    def get_supported_processor_types(cls, bundle_type: BundleType) -> list[str]:
        """
        Get list of supported processor types for a given bundle type.
        
        Args:
            bundle_type: The bundle type to check
            
        Returns:
            List of supported processor types
        """
        bundle_processors = cls._PROCESSOR_MAP.get(bundle_type, {})
        return list(bundle_processors.keys())

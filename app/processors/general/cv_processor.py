"""
General CV processor for page processing.
"""
from __future__ import annotations
from typing import Dict, Any, Union
import json
from datetime import datetime

# Application imports
from app.log import logger
from app.processors.base import BaseProcessor

# Type aliases
JSONDict = Dict[str, Any]


class GeneralCvProcessor(BaseProcessor):
    """
    CV processor for general bundle type.
    Provides basic object detection without complex analysis.
    """

    async def process(self, bucket: str, object_key: str, **kwargs) -> Union[Dict[str, Any], str]:
        """
        Process CV on an image stored in MinIO.
        For general bundle type, we provide basic object detection.

        Args:
            bucket: MinIO bucket name
            object_key: Object key in the bucket
            **kwargs: Additional parameters (not used for general processing)

        Returns:
            JSON string with basic object detection results

        Raises:
            Exception: If CV processing fails at any stage
        """
        return  # genernal no do any CV analysis
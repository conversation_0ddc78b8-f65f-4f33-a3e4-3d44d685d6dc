"""
Base processor class for bundle processing.
"""
from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Dict, Any, Union
import asyncio
import re

from app.utils.minio_client import MinioClient
from app.log import logger


class BaseProcessor(ABC):
    """
    Base class for all processors.
    Provides common interface and utility methods.
    """

    def __init__(self):
        """Initialize the processor."""
        pass

    @abstractmethod
    async def process(self, bucket: str, object_key: str, **kwargs) -> Union[Dict[str, Any], str, None]:
        """
        Process the image from MinIO.

        Args:
            bucket: MinIO bucket name
            object_key: Object key in the bucket
            **kwargs: Additional processing parameters

        Returns:
            Processing result as Dict or str

        Raises:
            Exception: If processing fails
        """
        pass

    async def _get_image_from_minio(self, bucket: str, object_key: str) -> bytes:
        """
        Get image data from MinIO.
        Common utility method to eliminate code duplication.

        Args:
            bucket: MinIO bucket name
            object_key: Object key in the bucket

        Returns:
            Raw image data as bytes

        Raises:
            Exception: If image retrieval fails
        """
        try:
            # Get MinioClient instance
            minio_client = MinioClient()

            # Get file content from MinIO
            logger.debug(f"Fetching image from MinIO: {bucket}/{object_key}")
            image_data = await asyncio.to_thread(
                minio_client.get,
                bucket=bucket,
                fnm=object_key
            )

            if not image_data:
                error_msg = f"Image data not found for {bucket}/{object_key}"
                logger.error(error_msg)
                raise Exception(error_msg)

            return image_data

        except Exception as e:
            logger.error(f"Error fetching image from MinIO {bucket}/{object_key}: {e}")
            raise

    def _extract_json_from_response(self, response_text: str) -> str:
        """
        Extract JSON content from LLM response that may contain markdown formatting.

        Args:
            response_text: Raw response text from LLM

        Returns:
            Cleaned JSON string
        """
        # First try to extract from ```json code blocks using the new method
        json_from_code_block = self._extract_code_block(response_text, "json")

        # If we got something different from the original text, it means we found a code block
        if json_from_code_block != response_text.strip():
            logger.debug("Found JSON content within ```json``` code blocks")
            return json_from_code_block

        # If no json code block found, try to find JSON-like content
        # Look for content that starts with { and ends with }
        json_start = response_text.find('{')
        if json_start != -1:
            # Find the last closing brace
            json_end = response_text.rfind('}')
            if json_end != -1 and json_end > json_start:
                extracted = response_text[json_start:json_end + 1].strip()
                logger.debug("Found JSON content within { }")
                return extracted

        logger.debug("No JSON structure found, returning original text")
        # If no JSON structure found, return the original text
        return response_text.strip()

    def _extract_code_block(self, response_text: str, block_type: str = None) -> str:
        """
        Extract content from code blocks in LLM response.
        Supports all types of code blocks (python, java, json, javascript, etc.)

        Args:
            response_text: Raw response text from LLM
            block_type: Type of code block to extract (e.g., "json", "python", "java", "javascript").
                       If None, will try to find any code block.

        Returns:
            Extracted content from code block or original text if not found
        """
        if block_type:
            # Pattern to match ```block_type ... ``` blocks
            pattern = rf'```{re.escape(block_type)}\s*(.*?)\s*```'

            # Try to find the specified block type
            match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
            if match:
                logger.debug(f"Found {block_type} content within ```{block_type}``` code blocks")
                extracted = match.group(1).strip()
                return extracted
        else:
            # Pattern to match any ```language ... ``` blocks
            # This will match ```python, ```java, ```json, ```javascript, etc.
            pattern = r'```\w*\s*(.*?)\s*```'

            # Try to find any code block
            match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
            if match:
                logger.debug("Found code block content within ``` ``` blocks")
                extracted = match.group(1).strip()
                return extracted

        logger.debug(f"No {block_type or 'code'} block found, returning original text")
        # If no code block found, return the original text
        return response_text.strip()

    def _determine_mime_type(self, image_data: bytes) -> str:
        """
        Determine MIME type from image data.

        Args:
            image_data: Raw image data

        Returns:
            MIME type string for base64 data URL
        """
        # Check for JPEG header
        if image_data.startswith(b'\xff\xd8\xff'):
            return "data:image/jpeg;base64"
        # Check for PNG header
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return "data:image/png;base64"
        # Check for GIF header
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
            return "data:image/gif;base64"
        # Check for WebP header
        elif len(image_data) > 12 and image_data[8:12] == b'WEBP':
            return "data:image/webp;base64"
        # Check for BMP header
        elif image_data.startswith(b'BM'):
            return "data:image/bmp;base64"
        # Default to JPEG
        else:
            return "data:image/jpeg;base64"

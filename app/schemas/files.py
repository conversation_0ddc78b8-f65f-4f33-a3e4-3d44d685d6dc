from typing import Optional, Dict, Any
from enum import Enum
from datetime import datetime
from pydantic import Field

from app.schemas.base import BaseSchema
from app.models.enums import ProcessingStatus

class FileCreate(BaseSchema):
    """
    Schema for creating a file entry.
    Only parent_id, original_name, and optional custom_metadata are required.
    user_id is optional.
    """
    parent_id: Optional[int] = Field(None, description="父级目录ID (不传或null表示根目录)")
    original_name: str = Field(..., description="原始文件名 (上传时)")
    description: Optional[str] = Field(None, description="文件或目录描述")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="用户自定义元数据")
    user_id: Optional[int] = Field(None, description="用户ID")

class FileUpdate(BaseSchema):
    """
    Schema for updating a file entry.
    Only id is required, all other fields are optional.
    """
    id: int = Field(..., description="文件ID")
    parent_id: Optional[int] = Field(None, description="父级目录ID")
    name: Optional[str] = Field(None, description="文件名")
    description: Optional[str] = Field(None, description="文件或目录描述")
    page_count: Optional[int] = Field(None, description="页数 (例如PDF)")
    mime_type: Optional[str] = Field(None, description="MIME类型")
    status_processing: Optional[ProcessingStatus] = Field(None, description="后台处理状态")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="用户自定义元数据")
    user_id: Optional[int] = Field(None, description="用户ID")

class FileRead(BaseSchema):
    """
    Base schema for file output.
    Contains all the fields that are returned when a file is retrieved.
    """
    id: int = Field(..., description="文件ID")
    name: str = Field(..., description="文件名")
    parent_id: Optional[int] = Field(None, description="父级目录ID")
    is_directory: bool = Field(..., description="是否为目录")
    original_name: Optional[str] = Field(None, description="原始文件名")
    description: Optional[str] = Field(None, description="文件或目录描述")
    mime_type: Optional[str] = Field(None, description="MIME类型")
    file_extension: Optional[str] = Field(None, description="文件扩展名")
    size_bytes: Optional[int] = Field(None, description="文件大小 (字节)")
    content_hash: Optional[str] = Field(None, description="文件内容哈希")
    hash_algorithm: Optional[str] = Field(None, description="哈希算法")
    page_count: Optional[int] = Field(None, description="页数 (例如PDF)")
    bucket_name: str = Field(..., description="存储桶名称")
    object_key: Optional[str] = Field(None, description="对象存储路径")
    status_processing: Optional[ProcessingStatus] = Field(None, description="处理状态")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="自定义元数据")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(None, description="删除时间")
    user_id: Optional[int] = Field(None, description="用户ID")




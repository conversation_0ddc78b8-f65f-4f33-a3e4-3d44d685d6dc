from pydantic import BaseModel, Field
from typing import Dict, Any, Optional


class TaskStartRequest(BaseModel):
    """任务启动请求"""
    task_name: str = Field(..., description="任务名称")
    params: Dict[str, Any] = Field(..., description="任务参数")


class TaskResponse(BaseModel):
    """任务响应"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    result: Optional[Any] = Field(None, description="任务结果")


class BundleTaskParams(BaseModel):
    """Bundle任务参数"""
    bundle_id: int = Field(..., description="Bundle ID", ge=1)


class PageTaskParams(BaseModel):
    """Page任务参数"""
    page_id: int = Field(..., description="Page ID", ge=1)

from datetime import datetime
from typing import Dict, Any, Optional, List

from pydantic import BaseModel, Field

from app.models.enums import ProcessingStatus, BundleType
from app.schemas.base import BaseSchema


class BundleBase(BaseSchema):
    """Base schema for Bundle with common fields"""
    name: str = Field(..., description="Bundle名称")
    description: Optional[str] = Field(None, description="Bundle描述")
    bundle_type: BundleType = Field(default=BundleType.GENERAL, description="Bundle类型 (决定处理方式)")
    status_processing: ProcessingStatus = Field(default=ProcessingStatus.NEW, description="Bundle处理状态")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="Bundle自定义元数据 (JSON)")
    user_id: Optional[int] = Field(None, description="关联用户ID (Bundle所有者)")


class BundleCreate(BundleBase):
    """Schema for creating a new Bundle"""
    file_ids: Optional[List[int]] = Field(default=[], description="与Bundle关联的文件ID列表")


class BundleUpdate(BaseSchema):
    """Schema for updating an existing Bundle"""
    id: int = Field(..., description="Bundle ID")
    name: Optional[str] = Field(None, description="Bundle名称")
    description: Optional[str] = Field(None, description="Bundle描述")
    bundle_type: Optional[BundleType] = Field(None, description="Bundle类型 (决定处理方式)")
    status_processing: Optional[ProcessingStatus] = Field(None, description="Bundle处理状态")
    process_task_id: Optional[str] = Field(None, description="关联的异步任务ID，用于追踪处理进度")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="Bundle自定义元数据 (JSON)")
    processing_result: Optional[Dict[str, Any]|str] = Field(None, description="Bundle处理/解析后的结果 (JSON)")
    user_id: Optional[int] = Field(None, description="关联用户ID (Bundle所有者)")
    file_ids: Optional[List[int]] = Field(None, description="与Bundle关联的文件ID列表")


class BundleRead(BaseSchema):
    """Schema for reading Bundle data"""
    id: int = Field(..., description="Bundle ID")
    name: str = Field(..., description="Bundle名称")
    description: Optional[str] = Field(None, description="Bundle描述")
    bundle_type: BundleType = Field(..., description="Bundle类型 (决定处理方式)")
    status_processing: ProcessingStatus = Field(..., description="Bundle处理状态")
    process_task_id: Optional[str] = Field(None, description="关联的异步任务ID，用于追踪处理进度")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="Bundle自定义元数据 (JSON)")
    processing_result: Optional[Dict[str, Any]|str] = Field(None, description="Bundle处理/解析后的结果 (JSON)")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(None, description="软删除时间标记")
    user_id: Optional[int] = Field(None, description="关联用户ID (Bundle所有者)")
    # Files will be handled separately to avoid circular imports

from datetime import datetime
from typing import Dict, Any, Optional

from pydantic import Field

from app.models.enums import ProcessingStatus
from app.schemas.base import BaseSchema


class PageBase(BaseSchema):
    """Base schema for Page with common fields"""
    bundle_id: int = Field(..., description="关联的Bundle ID")
    file_id: Optional[int] = Field(None, description="关联的文件ID")
    page_number: int = Field(..., description="bundle中的页码 (从1开始)")
    process_status: ProcessingStatus = Field(default=ProcessingStatus.NEW, description="内容解析状态")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="用户可编辑的元数据")


class PageCreate(PageBase):
    """Schema for creating a new Page"""
    process_result: Optional[Dict[str, Any]] = Field(None, description="解析结果的JSON表示")
    process_task_id: Optional[str] = Field(None, description="关联的异步任务ID")
    ocr_result: Optional[Dict[str, Any]|str] = Field(None, description="原始OCR分析结果")
    cv_result: Optional[Dict[str, Any]|str] = Field(None, description="原始版面图像分析结果")
    image_url: Optional[str] = Field(None, description="页面转换后的图像文件在对象存储的URL")
    image_width: Optional[int] = Field(None, description="图像宽度 (像素)")
    image_height: Optional[int] = Field(None, description="图像高度 (像素)")
    image_ppi: Optional[int] = Field(None, description="图像分辨率 (点/英寸)")


class PageUpdate(BaseSchema):
    """Schema for updating an existing Page"""
    id: int = Field(..., description="Page ID")
    bundle_id: Optional[int] = Field(None, description="关联的Bundle ID")
    file_id: Optional[int] = Field(None, description="关联的文件ID")
    page_number: Optional[int] = Field(None, description="bundle中的页码")
    process_status: Optional[ProcessingStatus] = Field(None, description="内容解析状态")
    process_result: Optional[Dict[str, Any]|str] = Field(None, description="解析结果的JSON表示")
    process_task_id: Optional[str] = Field(None, description="关联的异步任务ID")
    ocr_result: Optional[Dict[str, Any]|str] = Field(None, description="原始OCR分析结果")
    cv_result: Optional[Dict[str, Any]|str] = Field(None, description="原始版面图像分析结果")
    image_url: Optional[str] = Field(None, description="页面转换后的图像文件在对象存储的URL")
    image_width: Optional[int] = Field(None, description="图像宽度 (像素)")
    image_height: Optional[int] = Field(None, description="图像高度 (像素)")
    image_ppi: Optional[int] = Field(None, description="图像分辨率 (点/英寸)")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="用户可编辑的元数据")


class PageRead(BaseSchema):
    """Schema for reading Page data"""
    id: int = Field(..., description="Page ID")
    bundle_id: int = Field(..., description="关联的Bundle ID")
    file_id: Optional[int] = Field(None, description="关联的文件ID")
    page_number: int = Field(..., description="bundle中的页码")
    process_status: ProcessingStatus = Field(..., description="内容解析状态")
    process_result: Optional[Dict[str, Any]|str] = Field(None, description="解析结果的JSON表示")
    process_task_id: Optional[str] = Field(None, description="关联的异步任务ID")
    ocr_result: Optional[Dict[str, Any]|str] = Field(None, description="原始OCR分析结果")
    cv_result: Optional[Dict[str, Any]|str] = Field(None, description="原始版面图像分析结果")
    image_url: Optional[str] = Field(None, description="页面转换后的图像文件在对象存储的URL")
    image_width: Optional[int] = Field(None, description="图像宽度 (像素)")
    image_height: Optional[int] = Field(None, description="图像高度 (像素)")
    image_ppi: Optional[int] = Field(None, description="图像分辨率 (点/英寸)")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="用户可编辑的元数据")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

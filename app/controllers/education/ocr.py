"""
OCR utilities for page processing using vision LLM.
"""
from __future__ import annotations
from typing import Dict, Any
import asyncio
import base64
import json
import re

# Application imports
from app.log import logger
from app.utils.minio_client import MinioClient
from app.ai.llms.llm import get_llm_by_type
from app.ai.prompts.template import get_prompt_template

# Type aliases
JSONDict = Dict[str, Any]


class OCRService:
    """
    Service for performing OCR on images using LLM vision models.
    """
    # OCR with answer analysis prompt template name
    OCR_WITH_ANSWER_TEMPLATE = "ocr_with_answer"

    # OCR without answer analysis prompt template name
    OCR_WITHOUT_ANSWER_TEMPLATE = "ocr_without_answer"

    @staticmethod
    def _extract_json_from_response(response_text: str) -> str:
        """
        Extract JSON content from LLM response that may contain markdown formatting.

        Args:
            response_text: Raw response text from LLM

        Returns:
            Cleaned JSON string
        """

        # Pattern to match ```json ... ``` blocks
        json_pattern = r'```json\s*(.*?)\s*```'

        # Try to find JSON block first
        match = re.search(json_pattern, response_text, re.DOTALL | re.IGNORECASE)
        if match:
            logger.debug("Found JSON content within ```json``` markdown blocks")
            extracted = match.group(1).strip()
            return extracted

        # If no markdown block found, try to find JSON-like content
        # Look for content that starts with { and ends with }
        json_start = response_text.find('{')
        if json_start != -1:
            # Find the last closing brace
            json_end = response_text.rfind('}')
            if json_end != -1 and json_end > json_start:
                extracted = response_text[json_start:json_end + 1].strip()
                logger.debug("Found JSON content within { }")
                return extracted

        logger.debug("No JSON structure found, returning original text")
        # If no JSON structure found, return the original text
        return response_text.strip()

    @staticmethod
    async def _perform_ocr_with_vision_llm(
        image_data: bytes,
        use_answer_analysis: bool = False
    ) -> JSONDict:
        """
        Internal method to perform OCR on an image using vision LLM with structured analysis.

        Args:
            image_data: Raw image data as bytes
            use_answer_analysis: Whether to use the answer analysis prompt template

        Returns:
            Parsed JSON result from OCR

        Raises:
            Exception: If OCR processing fails
        """
        try:
            # Get vision LLM
            vision_llm = get_llm_by_type("vision")

            # Convert image to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')

            # Determine MIME type based on image header
            mime_type = OCRService._determine_mime_type(image_data)

            # Get prompt template
            if use_answer_analysis:
                prompt_content = get_prompt_template(OCRService.OCR_WITH_ANSWER_TEMPLATE)
            else:
                prompt_content = get_prompt_template(OCRService.OCR_WITHOUT_ANSWER_TEMPLATE)

            # Prepare messages for vision LLM
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt_content
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"{mime_type},{base64_image}"
                            }
                        }
                    ]
                }
            ]

            # Call vision LLM
            logger.debug("Calling vision LLM for OCR analysis...")
            response = await vision_llm.ainvoke(messages)
            logger.debug(f"Vision LLM response:\n\n {response.content})")

            # Extract JSON from response (handles ```json ... ``` formatting)
            cleaned_json = OCRService._extract_json_from_response(response.content)

            # if cleaned_json is not valid json, return it as pure string
            try:
                parsed_result = json.loads(cleaned_json)
                # TODO: should I return a dictory or just the raw json string?
                parsed_result = json.dumps(parsed_result, ensure_ascii=False, indent=4)
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse JSON response: {e}")
                parsed_result = cleaned_json
            
            return parsed_result  

        except Exception as e:
            logger.error(f"Error performing OCR with vision LLM: {e}")
            raise


    @staticmethod
    async def perform_ocr_from_minio(
        bucket: str,
        object_key: str,
        use_answer_analysis: bool = True
    ) -> JSONDict:
        """
        Perform OCR on an image stored in MinIO using vision LLM.
        This is the main public interface for OCR functionality.

        Args:
            bucket: MinIO bucket name
            object_key: Object key in the bucket
            use_answer_analysis: Whether to use the answer analysis prompt template

        Returns:
            Parsed JSON result from OCR analysis

        Raises:
            Exception: If OCR processing fails at any stage
        """
        try:
            # Get MinioClient instance
            minio_client = MinioClient()

            # Get file content from MinIO
            logger.debug(f"Fetching image from MinIO: {bucket}/{object_key}")
            image_data = await asyncio.to_thread(
                minio_client.get,
                bucket=bucket,
                fnm=object_key
            )

            if not image_data:
                error_msg = f"Image data not found for {bucket}/{object_key}"
                logger.error(error_msg)
                raise Exception(error_msg)

            # Perform OCR on the image data using vision LLM
            logger.debug(f"Starting OCR processing with use_answer_analysis={use_answer_analysis}")
            parsed_result = await OCRService._perform_ocr_with_vision_llm(
                image_data=image_data,
                use_answer_analysis=use_answer_analysis
            )

            logger.debug("OCR processing completed")
            return parsed_result

        except Exception as e:
            logger.error(f"Error performing OCR from MinIO: {e}")
            raise

    @staticmethod
    def _determine_mime_type(image_data: bytes) -> str:
        """
        Determine MIME type from image data.

        Args:
            image_data: Raw image data

        Returns:
            MIME type string
        """
        # Check for JPEG header
        if image_data.startswith(b'\xff\xd8\xff'):
            return "data:image/jpeg;base64"
        # Check for PNG header
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return "data:image/png;base64"
        # Check for GIF header
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
            return "data:image/gif;base64"
        # Default to JPEG
        else:
            return "data:image/jpeg;base64"



"""
Celery tasks for processing pages.
"""
from __future__ import annotations
from typing import Dict, Any
import asyncio

# Application imports
from app.settings import settings
from app.tasks import TaskManager, TaskServiceInterface, register_task
from app.models.enums import ProcessingStatus
from app.schemas.pages import PageUpdate
from app.schemas.tasks import PageTaskParams
from app.log import logger, ticker
from app.controllers.page import page_controller
from app.controllers.bundle import bundle_controller
from app.processors.factory import ProcessorFactory

# Old imports removed - now using ProcessorFactory


class OcrProcessError(Exception):
    """Custom exception for OCR processing errors."""

    def __init__(self, message: str, original_error: Exception = None):
        super().__init__(message)
        self.message = message
        self.original_error = original_error


class CVProcessError(Exception):
    """Custom exception for CV processing errors."""

    def __init__(self, message: str, original_error: Exception = None):
        super().__init__(message)
        self.message = message
        self.original_error = original_error


@register_task("page_process")
class PageService(TaskServiceInterface):
    """
    Service class for handling page processing operations.
    Encapsulates business logic for page processing.
    """

    @staticmethod
    async def start_task(params: Dict[str, Any]) -> Dict[str, Any]:
        """
        启动Page处理任务

        Args:
            params: 包含page_id的参数字典

        Returns:
            Dict with task_id and status
        """
        # 验证参数
        page_params = PageTaskParams(**params)
        page_id = page_params.page_id

        # Check if page exists
        page = await page_controller.get_page_info(page_id)

        # Check if page is already being processed
        if page.process_status in [ProcessingStatus.PROCESSING]:
            return {
                "task_id": page.process_task_id,
                "status": "already_processing",
                "message": f"Page {page_id} is already being processed"
            }

        # Update page with PENDING status, the task_id will be updated by the task itself by process_page
        page_update = PageUpdate(
            id=page_id,
            process_status=ProcessingStatus.PENDING
        )
        await page_controller.update_page(page_id, page_update)

        # Submit the task
        return await TaskManager.submit_task("tasks.page_processor", page_id)


    @staticmethod
    def stop_task(task_id: str) -> Dict[str, Any]:
        """
        停止Page处理任务

        Args:
            task_id: 任务ID

        Returns:
            Dict with cancellation status
        """
        return TaskManager.cancel_task(task_id)

    @staticmethod
    def status_task(task_id: str) -> Dict[str, Any]:
        """
        查询Page处理任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict with task status information
        """
        return TaskManager.get_task_status(task_id)

    @staticmethod
    async def on_timeout_task(task_id: str, **kwargs) -> None:
        """
        处理Page任务超时的回调方法

        Args:
            task_id: 任务ID
            **kwargs: 额外参数，应包含page_id
        """
        page_id = kwargs.get('page_id')
        if not page_id:
            logger.error(f"Page timeout handler called for task {task_id} but no page_id provided")
            return

        try:
            # 直接将page状态设置为FAILED，不需要查询任务状态
            page_update = PageUpdate(
                id=page_id,
                process_status=ProcessingStatus.FAILED
            )
            await page_controller.update_page(page_id, page_update)
            logger.warning(f"Page {page_id} marked as FAILED due to task {task_id} timeout")
        except Exception as exc:
            logger.error(f"Failed to update page {page_id} status after timeout: {exc}")

    @staticmethod
    async def update_page_status_from_task(page_id: int) -> None:
        """
        Update page status based on its task status.

        Args:
            page_id: ID of the page
        """
        page = await page_controller.get_page_info(page_id)

        task_status = TaskManager.get_task_status(page.process_task_id)

        # Update page status using PageController
        if task_status["status"] == "FAILURE":
            page_update = PageUpdate(id=page_id, process_status=ProcessingStatus.FAILED)
            await page_controller.update_page(page_id, page_update)
        elif task_status["status"] == "SUCCESS":
            page_update = PageUpdate(id=page_id, process_status=ProcessingStatus.COMPLETED)
            await page_controller.update_page(page_id, page_update)
        elif task_status["status"] == "REVOKED":
            page_update = PageUpdate(id=page_id, process_status=ProcessingStatus.CANCELLED)
            await page_controller.update_page(page_id, page_update)

    @staticmethod
    async def process_page(task_id: str, page_id: int) -> None:
        """
        Process a page asynchronously.

        Args:
            task_id: The Celery task ID
            page_id: ID of the page to process

        Raises:
            ValueError: If page not found, already processing, or missing image URL
            OcrProcessError: If OCR processing fails
            CVProcessError: If CV processing fails
        """
        # Get page and validate
        page = await page_controller.get_page_info(page_id)

        # Check if page is already being processed
        if page.process_status in [ProcessingStatus.PROCESSING]:
            raise ValueError(f"Page {page_id} is already being processed (status: {page.process_status})")

        # Check if image URL exists
        if not page.image_url:
            raise ValueError(f"Page {page_id} has no image URL")

        # Create PageUpdate object for status tracking
        page_update = PageUpdate(
            id=page.id,
            process_status=ProcessingStatus.PROCESSING,
            process_task_id = task_id,
            ocr_result=None,
            cv_result=None
        )

        try:
            # Update page status to PROCESSING
            await page_controller.update_page(page_id, page_update)

            # Get bundle information to determine processing type
            bundle = await bundle_controller.get_bundle_info(page.bundle_id)
            bundle_type = bundle.bundle_type

            logger.debug(f"Processing page {page_id} with bundle_type: {bundle_type}")

            with ticker(f"Processing page #{page.page_number}: id={page.id} ocr and cv"):
                # Get processors based on bundle type
                ocr_processor = ProcessorFactory.get_processor(bundle_type, "ocr")
                cv_processor = ProcessorFactory.get_processor(bundle_type, "cv")

                # Create concurrent tasks for OCR and CV processing using new processors
                ocr_task = ocr_processor.process(
                    bucket=settings.MINIO["bucket"],
                    object_key=page.image_url,
                    use_answer_analysis=False  # For education bundle type
                )
                cv_task = cv_processor.process(
                    bucket=settings.MINIO["bucket"],
                    object_key=page.image_url
                )

                # Execute tasks concurrently and collect results
                results = await asyncio.gather(ocr_task, cv_task, return_exceptions=True)
                ocr_result, cv_result = results

                # Check CV result - fail immediately if CV fails
                if isinstance(cv_result, Exception):
                    logger.error(f"CV processing failed for page {page_id}: {cv_result}")
                    raise CVProcessError(f"CV processing failed for page {page_id}", cv_result)
                else:
                    page_update.cv_result = cv_result

                # Check OCR result - fail immediately if OCR fails
                if isinstance(ocr_result, Exception):
                    logger.error(f"OCR processing failed for page {page_id}: {ocr_result}")
                    raise OcrProcessError(f"OCR processing failed for page {page_id}", ocr_result)
                else:
                    page_update.ocr_result = ocr_result

                # Both tasks succeeded - update results
                page_update.process_status = ProcessingStatus.COMPLETED

                logger.info(f"Page {page_id} processing completed successfully")

        except (OcrProcessError, CVProcessError):
            # Processing failed - update status to FAILED
            page_update.process_status = ProcessingStatus.FAILED
            raise

        except Exception as exc:
            # Unexpected error - update status to FAILED
            page_update.process_status = ProcessingStatus.FAILED
            logger.error(f"Unexpected error processing page {page_id}: {exc}")
            raise

        finally:
            # Always update the page with final status and results using PageController
            # update failure (like db constriant failure) will cause the status always in PROCESSING
            try :
                await page_controller.update_page(page_id, page_update)
            except Exception as exc:
                logger.error(f"Error updating page: {exc}")
                await page_controller.update_page(page_id, PageUpdate(id=page_id, process_status=ProcessingStatus.FAILED))
                raise










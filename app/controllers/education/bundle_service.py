"""
Service for processing bundles.
"""
from __future__ import annotations
from typing import Dict, List, Any, Optional, Tuple
import asyncio
import io

# Third-party imports
import cv2
import numpy as np
import fitz  # PyMuPDF

# Application imports
from app.tasks import TaskManager, TaskServiceInterface, register_task
from app.settings import settings
from app.models.enums import ProcessingStatus
from app.schemas.pages import PageCreate
from app.schemas.bundles import BundleUpdate
from app.schemas.files import FileRead
from app.schemas.tasks import BundleTaskParams
from app.utils.minio_client import MinioClient
from app.log import logger, ticker
from app.controllers.bundle import bundle_controller
from app.controllers.page import page_controller

@register_task("bundle_process")
class BundleService(TaskServiceInterface):
    """
    Service class for handling bundle processing operations.
    Encapsulates business logic for bundle processing.
    """
    # Default image size for resizing
    DEFAULT_IMAGE_WIDTH: int = 1200
    DEFAULT_IMAGE_HEIGHT: int = 1600
    DEFAULT_IMAGE_QUALITY: int = 85

    # Minio bucket for storing processed images
    PAGES_BUCKET: str = settings.MINIO["bucket"]
    PAGES_DIR: str = "pages"

    @staticmethod
    async def start_task(params: Dict[str, Any]) -> Dict[str, Any]:
        """
        启动Bundle处理任务

        Args:
            params: 包含bundle_id的参数字典

        Returns:
            Dict with task_id and status
        """
        # 验证参数
        bundle_params = BundleTaskParams(**params)
        bundle_id = bundle_params.bundle_id

        # Check if bundle exists using BundleController
        bundle = await bundle_controller.get_bundle_info(bundle_id)

        # Check if bundle is already being processed
        if bundle.status_processing in [ProcessingStatus.PROCESSING]:
            return {
                "task_id": bundle.process_task_id,
                "status": "already_processing",
                "message": f"Bundle {bundle_id} is already being processed"
            }

        # Update bundle status to PENDING using BundleController
        bundle_update = BundleUpdate(
            id=bundle_id,
            status_processing=ProcessingStatus.PENDING
        )
        await bundle_controller.update_bundle(bundle_id, bundle_update)

        # Submit the task，the task_id will be updated at the task itself by process_bundle.
        return await TaskManager.submit_task("tasks.bundle_processor", bundle_id)


    @staticmethod
    def stop_task(task_id: str) -> Dict[str, Any]:
        """
        停止Bundle处理任务

        Args:
            task_id: 任务ID

        Returns:
            Dict with cancellation status
        """
        return TaskManager.cancel_task(task_id)

    @staticmethod
    def status_task(task_id: str) -> Dict[str, Any]:
        """
        查询Bundle处理任务状态

        Args:
            task_id: 任务ID

        Returns:
            Dict with task status information
        """
        return TaskManager.get_task_status(task_id)


    @staticmethod
    async def on_timeout_task(task_id: str, **kwargs) -> None:
        """
        处理Bundle任务超时的回调方法

        Args:
            task_id: 任务ID
            **kwargs: 额外参数，应包含bundle_id
        """
        bundle_id = kwargs.get('bundle_id')
        if not bundle_id:
            logger.error(f"Bundle timeout handler called for task {task_id} but no bundle_id provided")
            return

        try:
            # 直接将bundle状态设置为FAILED，不需要查询任务状态
            bundle_update = BundleUpdate(
                id=bundle_id,
                status_processing=ProcessingStatus.FAILED
            )
            await bundle_controller.update_bundle(bundle_id, bundle_update)
            logger.warning(f"Bundle {bundle_id} marked as FAILED due to task {task_id} timeout")
        except Exception as exc:
            logger.error(f"Failed to update bundle {bundle_id} status after timeout: {exc}")

    @staticmethod
    async def _update_bundle_status_from_task(bundle_id: int) -> None:
        """
        Update bundle status based on its task status.

        Args:
            bundle_id: ID of the bundle
        """
        bundle = await bundle_controller.get_bundle_info(bundle_id)
        if not bundle.process_task_id:
            return

        task_status = TaskManager.get_task_status(bundle.process_task_id)

        # Update bundle status using BundleController
        if task_status["status"] == "FAILURE":
            bundle_update = BundleUpdate(id=bundle_id, status_processing=ProcessingStatus.FAILED)
            await bundle_controller.update_bundle(bundle_id, bundle_update)
        elif task_status["status"] == "SUCCESS":
            bundle_update = BundleUpdate(id=bundle_id, status_processing=ProcessingStatus.COMPLETED)
            await bundle_controller.update_bundle(bundle_id, bundle_update)
        elif task_status["status"] == "REVOKED":
            bundle_update = BundleUpdate(id=bundle_id, status_processing=ProcessingStatus.CANCELLED)
            await bundle_controller.update_bundle(bundle_id, bundle_update)

    @staticmethod
    async def process_bundle(task_id: str, bundle_id: int) -> None:
        """
        Process a bundle asynchronously.

        Args:
            task_id: The Celery task ID
            bundle_id: ID of the bundle to process

        Raises:
            ValueError: If bundle not found or already processing
            Exception: If processing fails
        """
        # Get bundle and validate using BundleController
        bundle = await bundle_controller.get_bundle_info(bundle_id)

        # Check if bundle is already being processed
        if bundle.status_processing in [ProcessingStatus.PROCESSING]:
            raise ValueError(f"Bundle {bundle_id} is already being processed (status: {bundle.status_processing})")


        bundle_update = BundleUpdate(
                id=bundle.id,
                status_processing=ProcessingStatus.PROCESSING,
                process_task_id=task_id
            )

        try:
            # Update bundle status to PROCESSING using BundleController
            await bundle_controller.update_bundle(bundle_id, bundle_update)

            # Get bundle files using BundleController
            all_files = await bundle_controller.get_bundle_files(bundle_id)

            # Filter out directories and files without object_key
            files = []
            if all_files:
                for file_read in all_files:
                    # Skip directories
                    if file_read.is_directory:
                        logger.debug(f"Skipping directory: {file_read.name} (ID: {file_read.id})")
                        continue

                    # Skip files without object_key
                    if not file_read.object_key:
                        logger.debug(f"Skipping file without object_key: {file_read.name} (ID: {file_read.id})")
                        continue

                    files.append(file_read)

            logger.info(f"Found {len(all_files) if all_files else 0} total files, {len(files)} processable files for bundle {bundle_id}")

            # Check if processable files is empty
            if not files:
                logger.warning(f"No processable files found for bundle {bundle_id}")
                bundle_update.status_processing = ProcessingStatus.COMPLETED
                return

            # Process each file
            page_count = 0
            page_creates = []
            for file_read in files:
                # file_read is already a FileRead object, pass it directly
                # Use the new process_file method that returns PageCreate objects
                file_pages = await BundleService._process_file(
                    bundle=bundle,
                    file=file_read,
                    start_page_number=page_count + 1
                )

                # Add pages from this file to the total
                if file_pages:
                    page_creates.extend(file_pages)
                    page_count = len(page_creates)

            # Clear existing pages for this bundle to maintain idempotency
            # Use BundleController's bulk delete method for better performance
            deleted_count = await bundle_controller.delete_pages(bundle_id)
            logger.info(f"Deleted {deleted_count} existing pages for bundle {bundle_id}")

            # Create Page records from PageCreate objects using PageController's bulk insert
            if page_creates:
                created_count = await page_controller.bulk_create_pages(page_creates)
                logger.info(f"Created {created_count} new pages for bundle {bundle_id}")

            # Trigger page processing for all pages
            pages = await bundle_controller.get_pages(bundle_id)

            with ticker(f"Processing {len(pages)} pages for bundle {bundle_id}"):
                # Import PageService here to avoid circular import
                from .page_service import PageService
                await asyncio.gather(*[
                    PageService.process_page(f"asynctask_page_{page.id}", page.id) for page in pages
                ])

            # Set success status for finally block
            bundle_update.status_processing = ProcessingStatus.COMPLETED
            logger.info(f"Bundle {bundle_id} processing completed successfully")

        except Exception as exc:
            # Log the error and keep failed status
            logger.error(f"Bundle processing failed for bundle {bundle_id}: {exc}")
            bundle_update.status_processing = ProcessingStatus.FAILED
            raise

        finally:
            # update failure (like db constriant failure) will cause the status always in PROCESSING
            try:
                await bundle_controller.update_bundle(bundle_id, bundle_update)
            except Exception as exc:
                logger.error(f"Error updating bundle: {exc}")
                await bundle_controller.update_bundle(bundle_id, BundleUpdate(id=bundle_id, status_processing=ProcessingStatus.FAILED))
                raise


    @staticmethod
    async def _process_file(
        bundle,
        file: FileRead,
        start_page_number: int = 1
    ) -> List[PageCreate]:
        """
        Process a single file within a bundle.

        Args:
            bundle: Bundle object (from BundleRead)
            file: FileRead object
            start_page_number: Starting page number (default: 1)

        Returns:
            List[PageCreate]: List of PageCreate objects
        """
        result_pages: List[PageCreate] = []
        current_page_number = start_page_number

        try:
            # Get MinioClient instance
            minio_client = MinioClient()

            # Check if file.object_key exists
            if not file.object_key:
                logger.error(f"File {file.id} has no object_key")
                return result_pages

            # Get file content from MinIO
            try:
                file_content = await asyncio.to_thread(
                    minio_client.get,
                    bucket=file.bucket_name,
                    fnm=file.object_key
                )
            except Exception as e:
                logger.error(f"Error retrieving file {file.id} from MinIO: {e}")
                return result_pages

            if not file_content:
                logger.error(f"File content not found for file {file.id}")
                return result_pages

            if file.mime_type and file.mime_type.startswith('image/'):
                # Process image file
                page_create = await BundleService._process_image_file(
                    bundle=bundle,
                    file=file,
                    file_content=file_content,
                    page_number=current_page_number
                )
                if page_create:
                    result_pages.append(page_create)

            elif file.mime_type == 'application/pdf':
                # Process PDF file
                pdf_pages = await BundleService._process_pdf_file(
                    bundle=bundle,
                    file=file,
                    file_content=file_content,
                    start_page_number=current_page_number
                )
                result_pages.extend(pdf_pages)

            else:
                # For other file types, log warning
                logger.warning(f"Unsupported file type: {file.mime_type} for file {file.id}")

        except Exception as e:
            logger.error(f"Error processing file {file.id}: {e}")
            # Continue processing other files

        return result_pages

    @staticmethod
    async def _process_image_file(
        bundle,
        file: FileRead,
        file_content: bytes,
        page_number: int
    ) -> Optional[PageCreate]:
        """
        Process an image file and create a PageCreate object.

        Args:
            bundle: Bundle object (from BundleRead)
            file: FileRead object
            file_content: Raw file content
            page_number: Page number

        Returns:
            Optional[PageCreate]: PageCreate object if successful
        """
        try:
            # Convert bytes to numpy array
            nparr = np.frombuffer(file_content, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if img is None:
                logger.error(f"Failed to decode image for file {file.id}")
                return None

            # Get original dimensions
            height, width = img.shape[:2]

            # Resize image while maintaining aspect ratio
            new_width, new_height = BundleService._calculate_resize_dimensions(
                width, height,
                BundleService.DEFAULT_IMAGE_WIDTH,
                BundleService.DEFAULT_IMAGE_HEIGHT
            )

            resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)

            # Create a unique object key for the page image in MinIO
            object_key = f"{BundleService.PAGES_DIR}/{bundle.id}/{file.id}/{page_number}.jpg"

            # Encode image to JPEG
            _, buffer = cv2.imencode('.jpg', resized_img, [cv2.IMWRITE_JPEG_QUALITY, BundleService.DEFAULT_IMAGE_QUALITY])
            img_bytes = buffer.tobytes()

            # Save to MinIO
            minio_client = MinioClient()
            await asyncio.to_thread(
                minio_client.put,
                bucket=BundleService.PAGES_BUCKET,
                fnm=object_key,
                binary=img_bytes
            )

            # Create PageCreate object
            return PageCreate(
                bundle_id=bundle.id,
                file_id=file.id,
                page_number=page_number,
                process_status=ProcessingStatus.NEW,
                image_url=object_key,
                image_width=new_width,
                image_height=new_height,
                custom_metadata={
                    "source_file_name": file.name,
                    "source_file_type": file.mime_type,
                    "original_width": width,
                    "original_height": height,
                    "created_from": "image_file"
                }
            )

        except Exception as e:
            logger.error(f"Error processing image file {file.id}: {e}")
            return None

    @staticmethod
    async def _process_pdf_file(
        bundle,
        file: FileRead,
        file_content: bytes,
        start_page_number: int = 1
    ) -> List[PageCreate]:
        """
        Process a PDF file and create PageCreate objects for each page.

        Args:
            bundle: Bundle object (from BundleRead)
            file: FileRead object
            file_content: Raw file content
            start_page_number: Starting page number (default: 1)

        Returns:
            List[PageCreate]: List of PageCreate objects
        """
        result_pages: List[PageCreate] = []
        current_page_number = start_page_number

        try:
            # Create a memory buffer for the PDF
            mem_buffer = io.BytesIO(file_content)

            # Open the PDF with PyMuPDF
            pdf_document = fitz.open(stream=mem_buffer, filetype="pdf")

            # Process each page
            for page_idx in range(len(pdf_document)):
                try:
                    # Get the page
                    pdf_page = pdf_document[page_idx]

                    # Render page to an image (pixmap)
                    # Higher zoom factors give better quality images
                    zoom_factor = 2.0  # Adjust as needed for quality vs. size
                    matrix = fitz.Matrix(zoom_factor, zoom_factor)
                    pixmap = pdf_page.get_pixmap(matrix=matrix, alpha=False)

                    # Convert pixmap to OpenCV image
                    img_bytes = pixmap.tobytes("jpeg", BundleService.DEFAULT_IMAGE_QUALITY)
                    nparr = np.frombuffer(img_bytes, np.uint8)
                    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                    if img is None:
                        logger.error(f"Failed to decode PDF page {page_idx+1} for file {file.id}")
                        continue

                    # Get dimensions
                    height, width = img.shape[:2]

                    # Resize image while maintaining aspect ratio
                    new_width, new_height = BundleService._calculate_resize_dimensions(
                        width, height,
                        BundleService.DEFAULT_IMAGE_WIDTH,
                        BundleService.DEFAULT_IMAGE_HEIGHT
                    )

                    resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)

                    # Create a unique object key for the page image in MinIO
                    object_key = f"{BundleService.PAGES_DIR}/{bundle.id}/{file.id}/{current_page_number}.jpg"

                    # Encode image to JPEG
                    _, buffer = cv2.imencode('.jpg', resized_img, [cv2.IMWRITE_JPEG_QUALITY, BundleService.DEFAULT_IMAGE_QUALITY])
                    img_bytes = buffer.tobytes()

                    # Save to MinIO
                    minio_client = MinioClient()
                    await asyncio.to_thread(
                        minio_client.put,
                        bucket=BundleService.PAGES_BUCKET,
                        fnm=object_key,
                        binary=img_bytes
                    )

                    # Get page dimensions from PDF
                    pdf_width, pdf_height = pdf_page.rect.width, pdf_page.rect.height

                    # Create PageCreate object
                    page_create = PageCreate(
                        bundle_id=bundle.id,
                        file_id=file.id,
                        page_number=current_page_number,
                        process_status=ProcessingStatus.NEW,
                        image_url=object_key,
                        image_width=new_width,
                        image_height=new_height,
                        custom_metadata={
                            "source_file_name": file.name,
                            "source_file_type": "application/pdf",
                            "pdf_page_number": page_idx + 1,
                            "pdf_width": pdf_width,
                            "pdf_height": pdf_height,
                            "created_from": "pdf_file"
                        }
                    )

                    result_pages.append(page_create)
                    current_page_number += 1

                except Exception as e:
                    logger.error(f"Error processing PDF page {page_idx+1} for file {file.id}: {e}")
                    continue

            # Close the PDF document
            pdf_document.close()

        except Exception as e:
            logger.error(f"Error processing PDF file {file.id}: {e}")

        return result_pages

    @staticmethod
    def _calculate_resize_dimensions(
        original_width: int,
        original_height: int,
        target_width: int,
        target_height: int
    ) -> Tuple[int, int]:
        """
        Calculate new dimensions while maintaining aspect ratio.

        Args:
            original_width: Original image width
            original_height: Original image height
            target_width: Target maximum width
            target_height: Target maximum height

        Returns:
            Tuple[int, int]: New width and height
        """
        # Calculate aspect ratios
        width_ratio = target_width / original_width
        height_ratio = target_height / original_height

        # Use the smaller ratio to ensure the image fits within the target dimensions
        # Not zoom up
        ratio = min(width_ratio, height_ratio, 1.0)

        # Calculate new dimensions
        new_width = int(original_width * ratio)
        new_height = int(original_height * ratio)

        return new_width, new_height





import os
import hashlib
import mimetypes
import asyncio
from datetime import datetime, timezone
from typing import Optional

from fastapi import HTTPEx<PERSON>, UploadFile
from tortoise.expressions import Q

from typing import List, Tuple

from app.core.crud import CRUDBase, Total
from app.models.file import File
from app.models.page import Page
from app.schemas.files import FileCreate, FileUpdate, FileRead
from app.schemas.pages import PageRead
from app.utils.minio_client import minioClient
from app.settings.config import settings
from app.log import logger


class FileController(CRUDBase[File, FileCreate, FileUpdate]):
    """
    Controller for file operations.
    Handles file uploads, metadata management, and interactions with Min<PERSON>.
    """

    def __init__(self):
        super().__init__(model=File)
        self.minio_client = minioClient

    async def upload_file(self, file: UploadFile, file_create: FileCreate) -> FileRead:
        """
        Upload a file to MinIO and create metadata in the database.

        Args:
            file: The uploaded file
            file_create: File creation data

        Returns:
            FileBase: The created file metadata
        """
        try:
            # Check if parent_id is provided and verify it's a directory
            if file_create.parent_id is not None:
                parent = await self.model.get_or_none(id=file_create.parent_id, deleted_at__isnull=True)
                if not parent:
                    raise HTTPException(status_code=404, detail=f"Parent directory with ID {file_create.parent_id} not found")
                if not parent.is_directory:
                    raise HTTPException(status_code=400, detail=f"Parent ID {file_create.parent_id} is not a directory")
                logger.debug(f"Verified parent directory: {parent.name} (ID: {parent.id})")

            # Extract file data
            file_content = await file.read()
            if not file_content:
                raise ValueError("Empty file content")

            # Get file metadata
            original_name = file_create.original_name or file.filename
            file_size = len(file_content)
            mime_type = file.content_type or mimetypes.guess_type(original_name)[0] or "application/octet-stream"
            file_extension = os.path.splitext(original_name)[1].lower() if original_name else ""

            # Calculate hash
            content_hash = hashlib.new(settings.HASH_ALGORITHM, file_content).hexdigest()

            # Get the full path based on parent_id and use it for the object key
            parent_path = await self.get_full_path(file_create.parent_id)
            object_key = f"{parent_path}{original_name}"
            # Remove any double slashes that might occur
            object_key = object_key.replace("//", "/")

            # Ensure user_id is None if it's 0 or not provided
            user_id = None if file_create.user_id == 0 or file_create.user_id is None else file_create.user_id

            # Log the user_id for debugging
            logger.info(f"Uploading file with user_id: {user_id} (original: {file_create.user_id})")

            # Upload to MinIO asynchronously using asyncio.to_thread
            # This runs the synchronous put method in a separate thread to avoid blocking the event loop
            await asyncio.to_thread(
                self.minio_client.put,
                bucket=settings.MINIO["bucket"],
                fnm=object_key,
                binary=file_content
            )

            # Create file metadata in database
            obj_data = {
                "name": original_name,  # Default to original_name, can be customized later
                "parent_id": file_create.parent_id,
                "is_directory": False,
                "original_name": original_name,
                "mime_type": mime_type,
                "file_extension": file_extension,
                "size_bytes": file_size,
                "content_hash": content_hash,
                "hash_algorithm": settings.HASH_ALGORITHM,
                "bucket_name": settings.MINIO["bucket"],
                "object_key": object_key,
                "custom_metadata": file_create.custom_metadata,
                "status_processing": "COMPLETED",  # Default status
                "user_id": user_id  # Use the sanitized user_id
            }

            # Create the file record
            obj = await self.model.create(**obj_data)

            # Return the file metadata
            return FileRead.model_validate(obj)

        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            raise

    async def create_directory(self, file_create: FileCreate) -> FileRead:
        """
        Create a directory metadata record in the database.

        Args:
            file_create: Directory creation data

        Returns:
            FileBase: Created directory metadata
        """
        try:
            # Check if parent_id is provided and verify it's a directory
            if file_create.parent_id is not None:
                parent = await self.model.get_or_none(id=file_create.parent_id, deleted_at__isnull=True)
                if not parent:
                    raise HTTPException(status_code=404, detail=f"Parent directory with ID {file_create.parent_id} not found")
                if not parent.is_directory:
                    raise HTTPException(status_code=400, detail=f"Parent ID {file_create.parent_id} is not a directory")
                logger.info(f"Verified parent directory: {parent.name} (ID: {parent.id})")

            # Check for duplicates - use original_name as the directory name
            existing = await self.model.filter(
                name=file_create.original_name,
                parent_id=file_create.parent_id,
                deleted_at__isnull=True
            ).first()

            if existing:
                raise HTTPException(
                    status_code=409,
                    detail=f"Directory with name '{file_create.original_name}' already exists in this location."
                )

            # Ensure user_id is None if it's 0 or not provided
            user_id = None if file_create.user_id == 0 or file_create.user_id is None else file_create.user_id

            # Log the user_id for debugging
            logger.info(f"Creating directory with user_id: {user_id} (original: {file_create.user_id})")

            # Prepare data for DB record creation
            obj_data = {
                "user_id": user_id,  # Use the sanitized user_id
                "name": file_create.original_name,
                "parent_id": file_create.parent_id,
                "is_directory": True,
                "custom_metadata": file_create.custom_metadata,
                "bucket_name": settings.MINIO["bucket"],
                "object_key": None,
                "size_bytes": 0,
                "mime_type": None,
                "content_hash": None,
                "hash_algorithm": None,
                "original_name": None,
                "page_count": None,
            }

            # Create the directory record
            obj = await self.model.create(**obj_data)

            # Return the directory metadata
            return FileRead.model_validate(obj)

        except Exception as e:
            logger.error(f"Error creating directory: {e}")
            raise

    async def update_file_info(self, file_id: int, file_update: FileUpdate) -> FileRead:
        """
        Update a file's metadata.

        Args:
            file_id: ID of the file to update
            file_update: Update data

        Returns:
            FileBase: Updated file metadata
        """
        try:
            # Get the file
            obj = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
            if not obj:
                raise HTTPException(status_code=404, detail="File not found for update")

            # Prepare update data
            update_data = file_update.model_dump(exclude_unset=True, exclude={"id"})

            # Check if parent_id is being updated and verify it's a directory
            if "parent_id" in update_data and update_data["parent_id"] is not None:
                parent = await self.model.get_or_none(id=update_data["parent_id"], deleted_at__isnull=True)
                if not parent:
                    raise HTTPException(status_code=404, detail=f"Parent directory with ID {update_data['parent_id']} not found")
                if not parent.is_directory:
                    raise HTTPException(status_code=400, detail=f"Parent ID {update_data['parent_id']} is not a directory")
                logger.info(f"Verified parent directory for update: {parent.name} (ID: {parent.id})")

            # Check for name conflicts if name is changing
            if "name" in update_data and update_data["name"] != obj.name:
                parent_id = obj.parent_id if "parent_id" not in update_data else update_data["parent_id"]
                existing = await self.model.filter(
                    name=update_data["name"],
                    parent_id=parent_id,
                    deleted_at__isnull=True
                ).first()

                if existing and existing.id != obj.id:
                    raise HTTPException(
                        status_code=409,
                        detail=f"{'Directory' if obj.is_directory else 'File'} with name '{update_data['name']}' already exists in this location."
                    )

            # Update the file metadata
            obj.update_from_dict(update_data)
            await obj.save(update_fields=list(update_data.keys()))

            # Return the updated file metadata
            return FileRead.model_validate(obj)

        except Exception as e:
            logger.error(f"Error updating file: {e}")
            raise



    async def soft_delete(self, file_id: int) -> FileRead:
        """
        Soft delete a file or directory by setting the deleted_at field.
        Does not delete the actual file from MinIO or remove database records.

        Args:
            file_id: ID of the file or directory to soft delete

        Returns:
            FileBase: The soft-deleted file metadata
        """
        try:
            # Get the file
            obj = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
            if not obj:
                raise HTTPException(status_code=404, detail="File not found for soft deletion")

            # Check if already deleted
            if obj.deleted_at is not None:
                return FileRead.model_validate(obj)  # Already deleted

            # Set the deleted_at field to the current time
            now = datetime.now(timezone.utc)  # Use UTC for consistency
            obj.deleted_at = now
            await obj.save(update_fields=["deleted_at"])

            # Return the updated file metadata
            return FileRead.model_validate(obj)

        except Exception as e:
            logger.error(f"Error soft deleting file: {e}")
            raise

    async def list_files_by_filter(self, parent_id: Optional[int] = None, name: Optional[str] = None,
                              include_deleted: bool = False, page: int = 1, page_size: int = 20) -> Tuple[Total, List[FileRead]]:
        """
        List files and directories based on filter criteria.
        Only filters by name and parent_id.

        Args:
            parent_id: Parent directory ID
            name: File name (fuzzy match)
            include_deleted: Whether to include deleted files
            page: Page number
            page_size: Page size

        Returns:
            Tuple[Total, List[FileRead]]: Total count and list of files matching the filter criteria
        """
        try:
            # Build query filters
            query_filters = Q()

            # Apply filters
            if parent_id is not None:
                query_filters &= Q(parent_id=parent_id)
            else:
                query_filters &= Q(parent_id__isnull=True)

            if name is not None:
                query_filters &= Q(name__icontains=name)

            # Handle deleted files
            if not include_deleted:
                query_filters &= Q(deleted_at__isnull=True)

            # Default ordering: directories first, then by most recently updated
            order_by = ["-is_directory", "-updated_at"]

            # Get total count and files
            total, files = await self.list(
                page=page,
                page_size=page_size,
                search=query_filters,
                order=order_by
            )

            # Convert to FileRead schema
            file_reads = []
            for file in files:
                file_reads.append(FileRead.model_validate(file))

            return total, file_reads

        except Exception as e:
            logger.error(f"Error listing files by filter: {e}")
            raise

    async def get_file_info(self, file_id: int) -> FileRead:
        """
        Get a file by ID.

        Args:
            file_id: File ID

        Returns:
            FileBase: File metadata
        """
        try:
            # Get the file
            obj = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
            if not obj:
                raise HTTPException(status_code=404, detail="File not found")

            # Return the file metadata
            return FileRead.model_validate(obj)

        except Exception as e:
            logger.error(f"Error getting file: {e}")
            raise

    async def get_full_path(self, parent_id: Optional[int]) -> str:
        """
        Recursively builds the full path string for a file/directory based on parent_id.
        Example: folder_a/subfolder_b/

        Args:
            parent_id: The parent directory ID

        Returns:
            str: The full path string with trailing slash (no leading slash for MinIO compatibility)
        """
        try:
            if parent_id is None:
                return ""

            path_parts = []
            current_parent_id = parent_id

            while current_parent_id is not None:
                # Get the current parent
                current_parent = await self.model.get_or_none(id=current_parent_id)
                if not current_parent:
                    break

                # Add the parent name to the path parts
                path_parts.insert(0, current_parent.name)

                # Move up to the next parent
                current_parent_id = current_parent.parent_id

            # Construct the path with trailing slash (no leading slash for MinIO)
            if path_parts:
                return "/".join(path_parts) + "/"
            else:
                return ""

        except Exception as e:
            logger.error(f"Error building full path: {e}")
            raise

    async def get_download_url(self, file_id: int, expires_seconds: int = 3600) -> str:
        """
        Generate a presigned download URL for a file.

        Args:
            file_id: File ID
            expires_seconds: URL expiration time in seconds

        Returns:
            str: Presigned download URL
        """
        try:
            # Get the file
            obj = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
            if not obj:
                raise HTTPException(status_code=404, detail="File not found")

            # Validate file
            if obj.is_directory or not obj.object_key:
                raise HTTPException(status_code=400, detail="Cannot generate download URL for a directory or file without object key")

            # Generate presigned URL
            url = self.minio_client.get_presigned_url(
                bucket=obj.bucket_name,
                fnm=obj.object_key,
                expires=expires_seconds
            )

            return url

        except Exception as e:
            logger.error(f"Error generating download URL: {e}")
            raise

    async def get_download_url_by_page_id(self, page_id: int, expires_seconds: int = 3600) -> str:
        """
        Generate a presigned download URL for a page's image.

        Args:
            page_id: Page ID
            expires_seconds: URL expiration time in seconds

        Returns:
            str: Presigned download URL for the page image
        """
        try:
            # Get the page
            page = await Page.get_or_none(id=page_id)
            if not page:
                raise HTTPException(status_code=404, detail="Page not found")

            # Validate page has image_url
            if not page.image_url:
                raise HTTPException(status_code=400, detail="Page does not have an associated image")

            # The image_url field contains the object_key for the page image
            # Generate presigned URL using the default bucket and the image_url as object_key
            url = self.minio_client.get_presigned_url(
                bucket=settings.MINIO["bucket"],
                fnm=page.image_url,
                expires=expires_seconds
            )

            return url

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error generating download URL for page {page_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to generate download URL for page: {str(e)}")

    async def get_pages(self, file_id: int) -> List[PageRead]:
        """
        Get all pages associated with a file.

        Args:
            file_id: File ID

        Returns:
            List[PageRead]: List of pages
        """
        try:
            # Verify that the file exists
            file = await self.model.get_or_none(id=file_id, deleted_at__isnull=True)
            if not file:
                raise HTTPException(status_code=404, detail=f"File with ID {file_id} not found")

            # Get pages for the file using the foreign key relationship
            await file.fetch_related("pages")
            pages = await file.pages.all().order_by("bundle_id", "page_number")

            # Convert to PageRead objects
            page_reads = []
            for page in pages:
                page_read = PageRead.model_validate(page)
                page_reads.append(page_read)

            return page_reads

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting file pages: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get file pages: {str(e)}")


# Instantiate the controller for use in your application
file_controller = FileController()
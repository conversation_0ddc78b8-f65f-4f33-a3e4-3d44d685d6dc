from typing import List, <PERSON><PERSON>, Optional

from fastapi import HTT<PERSON><PERSON>x<PERSON>
from tortoise.expressions import Q

from app.core.crud import CRUDBase, Total
from app.log import logger
from app.models.page import Page
from app.models.bundle import Bundle
from app.models.file import File
from app.schemas.pages import PageC<PERSON>, PageUpdate, PageRead


class PageController(CRUDBase[Page, PageCreate, PageUpdate]):
    """
    Controller for Page operations.
    Handles CRUD operations for Page entities.
    """

    def __init__(self):
        super().__init__(model=Page)

    async def create_page(self, page_in: PageCreate) -> PageRead:
        """
        Create a new Page.

        Args:
            page_in: Page creation data

        Returns:
            PageRead: Created page data
        """
        try:
            # Verify that the bundle exists
            bundle = await Bundle.get_or_none(id=page_in.bundle_id)
            if not bundle:
                raise HTTPException(status_code=404, detail=f"Bundle with ID {page_in.bundle_id} not found")

            # Verify that the file exists if file_id is provided
            if page_in.file_id is not None:
                file = await File.get_or_none(id=page_in.file_id)
                if not file:
                    raise HTTPException(status_code=404, detail=f"File with ID {page_in.file_id} not found")

            # Check if a page with the same bundle_id and page_number already exists
            existing_page = await self.model.get_or_none(
                bundle_id=page_in.bundle_id,
                page_number=page_in.page_number
            )
            if existing_page:
                raise HTTPException(
                    status_code=400,
                    detail=f"A page with page_number {page_in.page_number} already exists in bundle {page_in.bundle_id}"
                )

            # Create the page
            page_dict = page_in.model_dump()
            page = await self.create(page_dict)

            # Return the created page
            return PageRead.model_validate(page)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating page: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to create page: {str(e)}")

    async def update_page(self, page_id: int, page_update: PageUpdate) -> PageRead:
        """
        Update a Page.

        Args:
            page_id: ID of the page to update
            page_update: Update data

        Returns:
            PageRead: Updated page data
        """
        try:
            # Get the page
            page = await self.model.get_or_none(id=page_id)
            if not page:
                raise HTTPException(status_code=404, detail="Page not found")

            # If file_id is provided, verify that the file exists
            if page_update.file_id is not None:
                file = await File.get_or_none(id=page_update.file_id)
                if not file:
                    raise HTTPException(status_code=404, detail=f"File with ID {page_update.file_id} not found")

            # If bundle_id or page_number is being updated, check for conflicts
            if page_update.bundle_id is not None and page_update.page_number is not None:
                if page_update.bundle_id != page.bundle_id or page_update.page_number != page.page_number:
                    existing_page = await self.model.get_or_none(
                        bundle_id=page_update.bundle_id,
                        page_number=page_update.page_number
                    )
                    if existing_page and existing_page.id != page_id:
                        raise HTTPException(
                            status_code=400,
                            detail=f"A page with page_number {page_update.page_number} already exists in bundle {page_update.bundle_id}"
                        )

            # Update the page
            update_dict = page_update.model_dump(exclude={"id"}, exclude_unset=True, exclude_none=True)
            if update_dict:  # Only update if there are fields to update
                page = await self.update(id=page_id, obj_in=update_dict)

            # Return the updated page
            return PageRead.model_validate(page)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating page: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to update page: {str(e)}")

    async def list_pages(
        self,
        page: int = 1,
        page_size: int = 20,
        bundle_id: Optional[int] = None,
        file_id: Optional[int] = None,
        process_status: Optional[str] = None
    ) -> Tuple[Total, List[PageRead]]:
        """
        List pages with filtering options.

        Args:
            page: Page number
            page_size: Items per page
            bundle_id: Filter by bundle ID
            file_id: Filter by file ID
            process_status: Filter by processing status

        Returns:
            Tuple[Total, List[PageRead]]: Total count and list of pages
        """
        try:
            # Build query
            query = Q()

            # Apply filters
            if bundle_id is not None:
                query &= Q(bundle_id=bundle_id)
            if file_id is not None:
                query &= Q(file_id=file_id)
            if process_status is not None:
                query &= Q(process_status=process_status)

            # Execute query
            total, pages = await self.list(
                page=page,
                page_size=page_size,
                search=query,
                order=["-updated_at", "bundle_id", "page_number"]  # Most recently updated first
            )

            # Convert to PageRead objects
            page_reads = []
            for page_obj in pages:
                page_read = PageRead.model_validate(page_obj)
                page_reads.append(page_read)

            return total, page_reads

        except Exception as e:
            logger.error(f"Error listing pages: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to list pages: {str(e)}")

    async def get_page_info(self, page_id: int) -> PageRead:
        """
        Get detailed information about a page.

        Args:
            page_id: Page ID

        Returns:
            PageRead: Page data
        """
        try:
            # Get the page
            page = await self.model.get_or_none(id=page_id)
            if not page:
                raise HTTPException(status_code=404, detail="Page not found")

            # Convert to PageRead
            page_read = PageRead.model_validate(page)
            return page_read

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting page: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get page: {str(e)}")

    async def delete_page(self, page_id: int) -> bool:
        """
        Delete a page.

        Args:
            page_id: Page ID

        Returns:
            bool: True if successful
        """
        try:
            # Get the page
            page = await self.model.get_or_none(id=page_id)
            if not page:
                raise HTTPException(status_code=404, detail="Page not found")

            # Delete the page
            await self.remove(id=page_id)
            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting page: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to delete page: {str(e)}")

    async def bulk_create_pages(self, page_creates: List[PageCreate]) -> int:
        """
        Bulk create multiple pages for better performance.

        Args:
            page_creates: List of PageCreate objects

        Returns:
            int: Number of pages created
        """
        try:
            if not page_creates:
                return 0

            # Convert PageCreate objects to dictionaries for bulk insert
            page_dicts = []
            for page_create in page_creates:
                # Verify that the bundle exists
                bundle = await Bundle.get_or_none(id=page_create.bundle_id)
                if not bundle:
                    raise HTTPException(status_code=404, detail=f"Bundle with ID {page_create.bundle_id} not found")

                # Verify that the file exists if file_id is provided
                if page_create.file_id is not None:
                    file = await File.get_or_none(id=page_create.file_id)
                    if not file:
                        raise HTTPException(status_code=404, detail=f"File with ID {page_create.file_id} not found")

                page_dict = page_create.model_dump()
                page_dicts.append(page_dict)

            # Use bulk_create for better performance
            page_objects = [self.model(**page_dict) for page_dict in page_dicts]
            await self.model.bulk_create(page_objects)

            # Return the number of pages created
            return len(page_creates)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error bulk creating pages: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to bulk create pages: {str(e)}")

# Instantiate the controller for use in the application
page_controller = PageController()

import time
from contextlib import ContextDecorator

from .log import logger

class PerformanceTimer(ContextDecorator):
    """
    一个上下文管理器和装饰器，用于记录代码块的执行时间。
    """
    def __init__(self, block_name="Unnamed Block"):
        self.block_name = block_name
        self.start_time = None

    def __enter__(self):
        self.start_time = time.perf_counter() # 使用 perf_counter 获取高精度时间
        return self # 允许 'as timer:' 语法，虽然在这个简单例子中没用到timer对象

    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.perf_counter()
        duration_ms = (end_time - self.start_time) * 1000  # 转换为毫秒
        
        status_message = "completed successfully"
        if exc_type:
            status_message = f"exited with error: {exc_type.__name__}"
            #logger.error(f"Error in block '{self.block_name}': {exc_val}", exc_info=(exc_type, exc_val, exc_tb))

        logger.info(f"Exiting block: '{self.block_name}' - Status: {status_message} - Duration: {duration_ms:.3f} ms")
        # 返回 False (或不返回，默认也是 False) 会重新引发在块内发生的任何异常
        # 如果想抑制异常，可以返回 True
        return False
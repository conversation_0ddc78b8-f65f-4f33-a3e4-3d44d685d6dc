from tortoise import fields

from .base import BaseModel, TimestampMixin
from .enums import ProcessingStatus


class Page(BaseModel, TimestampMixin):
    """
    Model to store page data extracted from files.
    A page belongs to a single bundle and is associated with a file.
    """
    # --- Foreign Key Relationships ---
    bundle = fields.ForeignKeyField(
        "models.Bundle",
        related_name="pages",
        description="关联的Bundle",
        index=True,
        on_delete=fields.CASCADE  # If bundle is deleted, cascade delete pages
    )

    file = fields.ForeignKeyField(
        "models.File",
        related_name="pages",
        description="关联的文件",
        index=True,
        null=True,  # Allow null for SET_NULL to work
        on_delete=fields.SET_NULL  # If file is deleted, set file_id to NULL
    )

    # --- Core Attributes ---
    page_number = fields.IntField(
        description="bundle中的页码 (从1开始, 会根据文件上传的顺序重新编页)",
        index=True
    )

    # --- Processing Status and Results ---
    process_status = fields.CharEnumField(
        ProcessingStatus,
        max_length=20,
        description="内容解析状态",
        default=ProcessingStatus.NEW,
        index=True
    )

    process_result = fields.TextField(
        null=True,
        description="解析结果合并结果，包含OCR文本、结构信息等"
    )

    process_task_id = fields.CharField(
        max_length=64,
        null=True,
        description="关联的异步任务ID，用于追踪处理进度",
        index=True
    )

    # --- Analysis Results ---
    ocr_result = fields.TextField(
        null=True,
        description="原始OCR分析结果"
    )

    cv_result = fields.TextField(
        null=True,
        description="原始版面图像分析结果"
    )

    # --- Image Attributes ---
    image_url = fields.CharField(
        max_length=1024,
        null=True,
        description="页面转换后的图像文件在对象存储的URL"
    )

    image_width = fields.IntField(
        null=True,
        description="图像宽度 (像素)"
    )

    image_height = fields.IntField(
        null=True,
        description="图像高度 (像素)"
    )

    image_ppi = fields.IntField(
        null=True,
        description="图像分辨率 (点/英寸)"
    )

    # --- Custom Data ---
    custom_metadata = fields.JSONField(
        null=True,
        description="用户可编辑的元数据"
    )

    class Meta:
        table = "page"
        ordering = ["-updated_at"]
        # Ensure each page number is unique within a file
        unique_together = (("bundle_id", "page_number"),)

    def __str__(self):
        return f"Page {self.page_number} of File {self.file_id} in Bundle {self.bundle_id}"

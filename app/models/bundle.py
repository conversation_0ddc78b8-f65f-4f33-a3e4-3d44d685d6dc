from tortoise import fields
from enum import Enum

from .base import BaseModel, TimestampMixin
from .enums import ProcessingStatus, BundleType
#from .file import File


class Bundle(BaseModel, TimestampMixin):
    """
    Model to store logical collections of files (Bundles).
    """

    name = fields.CharField(max_length=255, description="Bundle名称", index=True)
    description = fields.TextField(null=True, description="Bundle描述")
    bundle_type = fields.CharEnumField(
        BundleType,
        max_length=30,
        description="Bundle类型 (决定处理方式)",
        index=True,
        default=BundleType.GENERAL
    )
    status_processing = fields.CharEnumField(
        ProcessingStatus,
        max_length=20,
        description="Bundle处理状态",
        default=ProcessingStatus.NEW
    )
    process_task_id = fields.CharField(
        max_length=64,
        null=True,
        description="关联的异步任务ID，用于追踪处理进度",
        index=True
    )
    custom_metadata = fields.JSONField(null=True, description="Bundle自定义元数据 (JSON)")
    processing_result = fields.TextField(null=True, description="Bundle处理/解析后的结果")
    deleted_at = fields.DatetimeField(null=True, index=True, description="软删除时间标记 (NULL表示未删除)")

    # --- 多对多关系定义 ---
    # 这个 'files' 字段定义了 Bundle 与 File 之间的多对多关系。
    files: fields.ManyToManyRelation["File"] = fields.ManyToManyField(
        "models.File",                 # 关联的模型名称 (字符串形式)
        related_name="bundles",        # 在 File 模型中反向访问此关系的名称
        description="与此Bundle关联的文件集合"
    )
    # file_associations: fields.ReverseRelation["BundleFile"] # 如果需要直接访问中间表记录

    user = fields.ForeignKeyField(
        "models.User",  # 指向 User 模型
        related_name="bundles",
        description="关联用户 (Bundle所有者)",
        null=True,
        on_delete=fields.SET_NULL
    )

    class Meta:
        table = "bundle"
        ordering = ["-updated_at"]
        # 可选的唯一约束
        # unique_together = (("user", "name", "deleted_at"),)

    def __str__(self):
        status_str = f" (Deleted)" if self.deleted_at else ""
        user_id_val = self.user_id  # Accessing the FK ID directly
        return f"Bundle: {self.name} (ID: {self.id}, Type: {self.bundle_type.value}, UserID: {user_id_val}){status_str}"
from typing import Optional
import re

import jwt
from fastapi import Depends, Header, HTTPException, Request

from app.core.ctx import CTX_USER_ID
from app.models import Role, User
from app.settings import settings
from app.log import logger


class AuthControl:
    @classmethod
    async def is_authed(cls, token: str = Header(..., description="token验证")) -> Optional["User"]:
        try:
            if token == "dev":
                user = await User.filter().first()
                user_id = user.id
            else:
                decode_data = jwt.decode(token, settings.SECRET_KEY, algorithms=settings.JWT_ALGORITHM)
                user_id = decode_data.get("user_id")
            user = await User.filter(id=user_id).first()
            if not user:
                raise HTTPException(status_code=401, detail="Authentication failed")
            CTX_USER_ID.set(int(user_id))
            return user
        except jwt.DecodeError:
            raise HTTPException(status_code=401, detail="无效的Token")
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="登录已过期")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"{repr(e)}")


class PermissionControl:
    # 用于匹配路径参数的正则表达式
    PATH_PARAM_PATTERN = re.compile(r'\{([^}]+)\}')
    
    @classmethod
    async def has_permission(cls, request: Request, current_user: User = Depends(AuthControl.is_authed)) -> None:
        """
        检查用户是否有权限访问当前请求的路径
        """
        # 超级用户跳过权限检查
        if current_user.is_superuser:
            return
            
        method = request.method
        request_path = request.url.path
        
        # 获取用户角色
        roles: list[Role] = await current_user.roles
        if not roles:
            raise HTTPException(status_code=403, detail="The user is not bound to a role")
            
        # 获取所有API权限
        apis = [await role.apis for role in roles]
        api_objects = sum(apis, [])
        
        # 检查是否有匹配的权限
        for api in api_objects:
            # 方法必须匹配
            if api.method != method:
                continue
                
            # 1. 检查精确匹配
            if api.path == request_path:
                return
                
            # 2. 检查参数化路径匹配
            if cls.is_path_match(api.path, request_path):
                return
        
        # 如果没有找到匹配的权限，则拒绝访问
        raise HTTPException(
            status_code=403, 
            detail=f"Permission denied method:{method} path:{request_path}"
        )
    
    @classmethod
    def is_path_match(cls, api_path: str, request_path: str) -> bool:
        """
        检查请求路径是否匹配API路径模式
        
        Args:
            api_path: API路径模式 (可能包含 {param} 形式的参数)
            request_path: 实际请求路径
            
        Returns:
            bool: 如果路径匹配则返回True，否则返回False
        """
        # 如果不包含参数占位符，则需要精确匹配
        if '{' not in api_path:
            return api_path == request_path
            
        # 将API路径模式转换为正则表达式
        # 例如: /api/v1/file/{id} -> ^/api/v1/file/([^/]+)$
        regex_pattern = api_path
        
        # 替换所有 {param} 为 ([^/]+)
        regex_pattern = cls.PATH_PARAM_PATTERN.sub(r'([^/]+)', regex_pattern)
        
        # 添加开始和结束锚点
        regex_pattern = f'^{regex_pattern}$'
        
        # 使用正则表达式匹配请求路径
        match = re.match(regex_pattern, request_path)
        
        # 调试日志
        logger.debug(f"Matching: API path={api_path}, Request path={request_path}, Regex={regex_pattern}, Match={bool(match)}")
        
        return bool(match)


DependAuth = Depends(AuthControl.is_authed)
DependPermisson = Depends(PermissionControl.has_permission)

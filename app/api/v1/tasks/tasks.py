from fastapi import APIRouter, Path

from app.tasks import TaskRegistry
from app.schemas.base import Success, Fail
from app.schemas.tasks import TaskStartRequest
from app.log import logger

router = APIRouter()


@router.post("/start", summary="启动任务")
async def start_task(request: TaskStartRequest):
    """
    启动任务 - 统一的任务启动接口

    支持的任务类型:
    - bundle_process: Bundle处理任务，params需要包含bundle_id
    - page_process: Page处理任务，params需要包含page_id
    """
    try:
        # 获取任务服务
        service_class = TaskRegistry.get_service(request.task_name)

        # 启动任务
        result = await service_class.start_task(request.params)

        return Success(data=result, msg=f"任务 {request.task_name} 已启动")
    except ValueError as e:
        return Fail(code=400, msg=str(e))
    except Exception as e:
        logger.exception(f"Error starting task {request.task_name}: {e}")
        return Fail(code=500, msg=f"启动任务失败: {str(e)}")


@router.post("/{task_id}/stop", summary="停止任务")
async def stop_task(
    task_id: str = Path(..., description="任务ID"),
):
    """
    停止正在执行的任务
    """
    try:
        # 由于我们不知道具体的任务类型，直接使用TaskManager
        from app.tasks import TaskManager
        result = TaskManager.cancel_task(task_id)
        return Success(data=result, msg="任务停止请求已提交")
    except Exception as e:
        logger.exception(f"Error stopping task: {e}")
        return Fail(code=500, msg=f"停止任务失败: {str(e)}")


@router.get("/{task_id}/status", summary="查询任务状态")
async def get_task_status(
    task_id: str = Path(..., description="任务ID"),
):
    """
    查询任务的当前状态和结果
    """
    try:
        # 由于我们不知道具体的任务类型，直接使用TaskManager
        from app.tasks import TaskManager
        result = TaskManager.get_task_status(task_id)
        return Success(data=result, msg="获取任务状态成功")
    except Exception as e:
        logger.exception(f"Error getting task status: {e}")
        return Fail(code=500, msg=f"获取任务状态失败: {str(e)}")


@router.get("/types", summary="获取支持的任务类型")
async def get_task_types():
    """
    获取所有支持的任务类型
    """
    try:
        task_types = TaskRegistry.get_available_tasks()
        return Success(data={"task_types": task_types}, msg="获取任务类型成功")
    except Exception as e:
        logger.exception(f"Error getting task types: {e}")
        return Fail(code=500, msg=f"获取任务类型失败: {str(e)}")

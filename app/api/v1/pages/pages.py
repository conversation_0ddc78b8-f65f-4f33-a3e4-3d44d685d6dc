from fastapi import APIRouter, Query, Path, Body, HTTPException
from typing import Optional, List

from app.controllers.page import page_controller
from app.models.enums import ProcessingStatus
from app.schemas.base import Success, Fail, SuccessExtra
from app.schemas.pages import PageCreate, PageUpdate
from app.log import logger

router = APIRouter()


@router.get("/list", summary="查看页面列表")
async def list_pages(
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
    bundle_id: Optional[int] = Query(None, description="Bundle ID"),
    file_id: Optional[int] = Query(None, description="文件ID"),
    process_status: Optional[str] = Query(None, description="处理状态"),
):
    """
    获取页面列表，支持分页和筛选
    """
    try:
        total, pages = await page_controller.list_pages(
            page=page,
            page_size=page_size,
            bundle_id=bundle_id,
            file_id=file_id,
            process_status=process_status
        )

        # Convert each PageRead object to a dictionary
        page_dicts = [page.model_dump(exclude_none=True) for page in pages]

        return SuccessExtra(
            data=page_dicts,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.exception(f"Error listing pages: {e}")
        return Fail(code=500, msg=f"获取页面列表失败: {str(e)}")


@router.get("/{page_id}", summary="获取页面详情")
async def get_page(
    page_id: int = Path(..., description="页面ID", ge=1),
):
    """
    获取单个页面的详细信息
    """
    try:
        page = await page_controller.get_page_info(page_id)
        return Success(data=page.model_dump(exclude_none=True))
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error getting page: {e}")
        return Fail(code=500, msg=f"获取页面详情失败: {str(e)}")


@router.post("/create", summary="创建页面")
async def create_page(
    page_in: PageCreate = Body(...),
):
    """
    创建新的页面
    """
    try:
        page = await page_controller.create_page(page_in)
        return Success(data=page.model_dump(exclude_none=True), msg="页面创建成功")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error creating page: {e}")
        return Fail(code=500, msg=f"创建页面失败: {str(e)}")


@router.post("/update", summary="更新页面")
async def update_page(
    page_update: PageUpdate = Body(...),
):
    """
    更新页面信息
    """
    try:
        page = await page_controller.update_page(page_update.id, page_update)
        return Success(data=page.model_dump(exclude_none=True), msg="页面更新成功")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error updating page: {e}")
        return Fail(code=500, msg=f"更新页面失败: {str(e)}")


@router.delete("/{page_id}", summary="删除页面")
async def delete_page(
    page_id: int = Path(..., description="页面ID", ge=1),
):
    """
    删除页面
    """
    try:
        result = await page_controller.delete_page(page_id)
        return Success(msg="页面已删除")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error deleting page: {e}")
        return Fail(code=500, msg=f"删除页面失败: {str(e)}")

from fastapi import APIRouter, Query, Path, Body, HTTPException
from typing import Optional

from app.controllers.bundle import bundle_controller
from app.models.enums import BundleType
from app.schemas.base import Success, Fail, SuccessExtra
from app.schemas.bundles import BundleCreate, BundleUpdate
from app.log import logger

router = APIRouter()

@router.get("/types", summary="获取Bundle类型列表")
async def get_bundle_types():
    """
    获取所有可用的Bundle类型
    """
    try:
        # Get all bundle types from the enum
        bundle_types = [{"value": t.value, "label": t.value} for t in BundleType]
        return Success(data=bundle_types)
    except Exception as e:
        logger.exception(f"Error getting bundle types: {e}")
        return Fail(code=500, msg=f"获取Bundle类型列表失败: {str(e)}")


@router.get("/list", summary="查看Bundle列表")
async def list_bundles(
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
    name: Optional[str] = Query(None, description="Bundle名称 (模糊匹配)"),
    bundle_type: Optional[str] = Query(None, description="Bundle类型"),
    user_id: Optional[int] = Query(None, description="用户ID"),
    include_deleted: bool = Query(False, description="是否包含已删除的Bundle"),
):
    """
    获取Bundle列表，支持分页和筛选
    """
    try:
        total, bundles = await bundle_controller.list_bundles(
            page=page,
            page_size=page_size,
            name=name,
            bundle_type=bundle_type,
            user_id=user_id,
            include_deleted=include_deleted
        )

        # Convert each BundleRead object to a dictionary
        bundle_dicts = [bundle.model_dump(exclude_none=True) for bundle in bundles]

        return SuccessExtra(
            data=bundle_dicts,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.exception(f"Error listing bundles: {e}")
        return Fail(code=500, msg=f"获取Bundle列表失败: {str(e)}")


@router.get("/{bundle_id}", summary="获取Bundle详情")
async def get_bundle(
    bundle_id: int = Path(..., description="Bundle ID", ge=1),
):
    """
    获取单个Bundle的详细信息
    """
    try:
        bundle = await bundle_controller.get_bundle_info(bundle_id)
        return Success(data=bundle.model_dump(exclude_none=True))
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error getting bundle: {e}")
        return Fail(code=500, msg=f"获取Bundle详情失败: {str(e)}")


@router.post("/create", summary="创建Bundle")
async def create_bundle(
    bundle_in: BundleCreate = Body(...),
):
    """
    创建新的Bundle
    """
    try:
        bundle = await bundle_controller.create_bundle(bundle_in)
        return Success(data=bundle.model_dump(exclude_none=True), msg="Bundle创建成功")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error creating bundle: {e}")
        return Fail(code=500, msg=f"创建Bundle失败: {str(e)}")


@router.post("/update", summary="更新Bundle")
async def update_bundle(
    bundle_update: BundleUpdate = Body(...),
):
    """
    更新Bundle信息
    """
    try:
        bundle = await bundle_controller.update_bundle(bundle_update.id, bundle_update)
        return Success(data=bundle.model_dump(exclude_none=True), msg="Bundle更新成功")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error updating bundle: {e}")
        return Fail(code=500, msg=f"更新Bundle失败: {str(e)}")


@router.delete("/{bundle_id}", summary="删除Bundle")
async def delete_bundle(
    bundle_id: int = Path(..., description="Bundle ID", ge=1),
):
    """
    软删除Bundle（设置deleted_at字段）
    """
    try:
        bundle = await bundle_controller.soft_delete(bundle_id)
        return Success(data=bundle.model_dump(exclude_none=True), msg="Bundle已删除")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error deleting bundle: {e}")
        return Fail(code=500, msg=f"删除Bundle失败: {str(e)}")


@router.post("/{bundle_id}/restore", summary="恢复已删除的Bundle")
async def restore_bundle(
    bundle_id: int = Path(..., description="Bundle ID", ge=1),
):
    """
    恢复已软删除的Bundle
    """
    try:
        bundle = await bundle_controller.restore(bundle_id)
        return Success(data=bundle.model_dump(exclude_none=True), msg="Bundle已恢复")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error restoring bundle: {e}")
        return Fail(code=500, msg=f"恢复Bundle失败: {str(e)}")


@router.get("/{bundle_id}/files", summary="获取Bundle关联的文件")
async def get_bundle_files(
    bundle_id: int = Path(..., description="Bundle ID", ge=1),
):
    """
    获取与Bundle关联的所有文件
    """
    try:
        files = await bundle_controller.get_bundle_files(bundle_id)
        file_dicts = [file.model_dump(exclude_none=True) for file in files]
        return Success(data=file_dicts)
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error getting bundle files: {e}")
        return Fail(code=500, msg=f"获取Bundle文件失败: {str(e)}")


@router.get("/{bundle_id}/pages", summary="获取Bundle关联的页面")
async def get_bundle_pages(
    bundle_id: int = Path(..., description="Bundle ID", ge=1),
):
    """
    获取与Bundle关联的所有页面
    """
    try:
        pages = await bundle_controller.get_pages(bundle_id)
        page_dicts = [page.model_dump(exclude_none=True) for page in pages]
        return Success(data=page_dicts)
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error getting bundle pages: {e}")
        return Fail(code=500, msg=f"获取Bundle页面失败: {str(e)}")


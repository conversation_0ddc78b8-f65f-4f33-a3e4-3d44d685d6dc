from fastapi import APIRouter, Body, Query, Path, UploadFile, File as FastApiFile, HTTPException, Form
from typing import Optional
import json

from app.controllers.file import file_controller
from app.schemas.base import Success, Fail, SuccessExtra
from app.schemas.files import FileCreate, FileUpdate
from app.log import logger

router = APIRouter()


@router.get("/list", summary="列出文件和目录")
async def list_files(
    parent_id: Optional[int] = Query(None, description="父级目录ID (不传或null表示根目录)"),
    name: Optional[str] = Query(None, description="文件名 (模糊匹配)"),
    include_deleted: bool = Query(False, description="是否包含已删除文件"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(20, description="每页数量", ge=1, le=100),
):
    """
    列出文件和目录，支持按父级目录ID和名称筛选
    """
    total, files = await file_controller.list_files_by_filter(
        parent_id=parent_id,
        name=name,
        include_deleted=include_deleted,
        page=page,
        page_size=page_size
    )

    # Convert each FileRead object to a dictionary
    file_dicts = [file.model_dump(exclude_none=True) for file in files]

    return SuccessExtra(data=file_dicts, total=total, page=page, page_size=page_size)


@router.get("/download-url", summary="获取文件下载链接")
async def get_download_url(
    file_id: Optional[int] = Query(None, description="文件ID", ge=1),
    page_id: Optional[int] = Query(None, description="页面ID", ge=1),
    expires_seconds: int = Query(3600, description="链接有效期（秒）", ge=60, le=86400),
):
    """
    获取文件或页面的预签名下载URL

    支持两种方式：
    - file_id: 直接通过文件ID获取下载链接
    - page_id: 通过页面ID获取页面图像的下载链接

    两个参数必须提供其中一个，不能同时提供或都不提供
    """
    try:
        # 验证参数
        if not file_id and not page_id:
            return Fail(code=400, msg="必须提供 file_id 或 page_id 参数")

        if file_id and page_id:
            return Fail(code=400, msg="不能同时提供 file_id 和 page_id 参数")

        if file_id:
            # 使用原有的文件下载逻辑
            url = await file_controller.get_download_url(file_id, expires_seconds)
        else:
            # 使用页面ID获取下载链接
            url = await file_controller.get_download_url_by_page_id(page_id, expires_seconds)

        return Success(data=url, msg="下载链接生成成功")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.error(f"Error generating download URL: {e}")
        return Fail(code=500, msg="下载链接生成失败")


@router.get("/{file_id}", summary="获取文件或目录详情")
async def get_file(
    file_id: int = Path(..., description="文件或目录ID", ge=1),
):
    """
    获取文件或目录的详细信息
    """
    try:
        result = await file_controller.get_file_info(file_id)
        # Use model_dump with exclude_none=True to avoid None values
        result_dict = result.model_dump(exclude_none=True)
        return Success(data=result_dict)
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error getting file: {e}")
        return Fail(code=500, msg="获取文件信息失败")


@router.post("/upload", summary="上传文件")
async def upload_file(
    file: UploadFile = FastApiFile(..., description="要上传的文件"),
    parent_id: Optional[int] = Form(None, description="父级目录ID (不传或null表示根目录)"),
    user_id: Optional[int] = Form(None, description="用户ID"),
    custom_metadata: Optional[str] = Form(None, description="自定义元数据 (JSON字符串)"),
):
    """
    上传文件并创建文件元数据

    - **file**: 要上传的文件
    - **parent_id**: 父级目录ID (不传或null表示根目录)
    - **user_id**: 用户ID
    - **custom_metadata**: 自定义元数据 (JSON字符串)
    """
    try:
        # 检查文件是否为空
        if not file or not file.filename:
            return Fail(code=400, msg="未提供文件或文件名为空")

        logger.info(f"Received file upload: {file.filename}, content_type: {file.content_type}")

        # 处理自定义元数据
        metadata = None
        if custom_metadata:
            try:
                metadata = json.loads(custom_metadata)
            except json.JSONDecodeError:
                return Fail(code=400, msg="自定义元数据格式错误，必须是有效的JSON")

        # 创建FileCreate对象
        file_create = FileCreate(
            parent_id=parent_id,
            original_name=file.filename,
            custom_metadata=metadata,
            user_id=user_id if user_id else None  # Ensure user_id is None if not provided
        )

        # 上传文件
        result = await file_controller.upload_file(file, file_create)
        # Use model_dump with exclude_none=True to avoid None values
        result_dict = result.model_dump(exclude_none=True)
        return Success(data=result_dict, msg="文件上传成功")
    except ValueError as e:
        logger.error(f"Value error in file upload: {e}")
        return Fail(code=400, msg=str(e))
    except Exception as e:
        logger.exception(f"Error uploading file: {e}")  # Add full exception info
        return Fail(code=500, msg=f"文件上传失败: {str(e)}")


@router.post("/directory", summary="创建目录")
async def create_directory(
    file_create: FileCreate = Body(...),
):
    """
    创建目录

    - **parent_id**: 父级目录ID (不传或null表示根目录)
    - **original_name**: 目录名称
    - **user_id**: 用户ID (可选)
    - **custom_metadata**: 自定义元数据 (可选)
    """
    try:
        # 确保user_id为None而不是0，避免外键约束错误
        if hasattr(file_create, 'user_id') and file_create.user_id == 0:
            file_create.user_id = None

        logger.info(f"Creating directory: {file_create.original_name}, parent_id: {file_create.parent_id}, user_id: {file_create.user_id}")

        result = await file_controller.create_directory(file_create)

        # Use model_dump with exclude_none=True to avoid None values
        result_dict = result.model_dump(exclude_none=True)
        logger.info(f"Directory created successfully with ID: {result_dict.get('id')}")

        return Success(data=result_dict, msg="目录创建成功")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error creating directory: {e}")  # Add full exception info
        return Fail(code=500, msg=f"目录创建失败: {str(e)}")


@router.put("/{file_id}", summary="更新文件或目录信息")
async def update_file(
    file_id: int = Path(..., description="文件或目录ID", ge=1),
    file_update: FileUpdate = Body(...),
):
    """
    更新文件或目录的元数据
    """
    try:
        # 确保file_update中的id与路径参数一致
        file_update.id = file_id
        result = await file_controller.update_file_info(file_id, file_update)
        # Use model_dump with exclude_none=True to avoid None values
        result_dict = result.model_dump(exclude_none=True)
        return Success(data=result_dict, msg="文件信息更新成功")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.error(f"Error updating file: {e}")
        return Fail(code=500, msg="文件信息更新失败")


@router.delete("/{file_id}", summary="软删除文件或目录")
async def soft_delete_file(
    file_id: int = Path(..., description="文件或目录ID", ge=1),
):
    """
    软删除文件或目录（设置deleted_at字段）
    """
    try:
        result = await file_controller.soft_delete(file_id)
        # Use model_dump with exclude_none=True to avoid None values
        result_dict = result.model_dump(exclude_none=True)
        return Success(data=result_dict, msg="文件已软删除")
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.error(f"Error soft deleting file: {e}")
        return Fail(code=500, msg="文件软删除失败")


@router.get("/{file_id}/pages", summary="获取文件关联的页面")
async def get_file_pages(
    file_id: int = Path(..., description="文件ID", ge=1),
):
    """
    获取与文件关联的所有页面
    """
    try:
        pages = await file_controller.get_pages(file_id)
        page_dicts = [page.model_dump(exclude_none=True) for page in pages]
        return Success(data=page_dicts)
    except HTTPException as e:
        return Fail(code=e.status_code, msg=e.detail)
    except Exception as e:
        logger.exception(f"Error getting file pages: {e}")
        return Fail(code=500, msg=f"获取文件页面失败: {str(e)}")
"""
Celery application configuration for asynchronous task processing.
"""
import asyncio
from celery import Celery
from celery.signals import worker_init, worker_shutdown
from tortoise import Tortoise
from app.settings.config import settings
from app.log import logger

# Create Celery application
celery_app = Celery(
    "app",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=900,  # 15mins
    worker_max_tasks_per_child=200,
    worker_prefetch_multiplier=4,
)

# Auto-discover tasks in the specified packages
celery_app.autodiscover_tasks(["app.tasks"])


@worker_init.connect
def init_worker(**kwargs):
    """Initialize Tortoise ORM when worker starts"""
    _ = kwargs  # Suppress unused parameter warning
    logger.info("Initializing Tortoise ORM in Celery worker...")

    async def init_tortoise():
        await Tortoise.init(config=settings.TORTOISE_ORM)
        logger.info("Tortoise ORM initialized in Celery worker")

    # Run the initialization in the worker's event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(init_tortoise())


@worker_shutdown.connect
def shutdown_worker(**kwargs):
    """Close Tortoise ORM connections when worker shuts down"""
    _ = kwargs  # Suppress unused parameter warning
    logger.info("Shutting down Tortoise ORM in Celery worker...")

    async def close_tortoise():
        await Tortoise.close_connections()
        logger.info("Tortoise ORM connections closed in Celery worker")

    # Close connections in the worker's event loop
    loop = asyncio.get_event_loop()
    if loop.is_running():
        # If loop is running, schedule the close operation
        asyncio.create_task(close_tortoise())
    else:
        # If loop is not running, run it directly
        loop.run_until_complete(close_tortoise())

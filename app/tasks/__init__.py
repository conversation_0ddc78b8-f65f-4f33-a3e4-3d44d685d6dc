"""
任务处理模块 - 统一管理所有任务相关功能
"""

# Celery 应用和配置
from .celery_app import celery_app

# 任务管理器
from .manager import TaskManager

# 任务注册表和接口
from .registry import TaskRegistry, TaskServiceInterface, register_task

# 工具函数
from .utils import run_async_in_celery, celery_async_task

# 后台任务管理
from .bgtask import BgTasks

__all__ = [
    # Celery 核心
    "celery_app",

    # 任务管理
    "TaskManager",

    # 任务注册
    "TaskRegistry",
    "TaskServiceInterface",
    "register_task",

    # 工具函数
    "run_async_in_celery",
    "celery_async_task",

    # 后台任务
    "BgTasks",
]

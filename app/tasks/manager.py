"""
Task manager for handling Celery tasks and their status.
"""
from .celery_app import celery_app
from app.log import logger
from celery.result import AsyncResult
from typing import Dict, Any


class TaskManager:
    """
    Task manager for handling Celery tasks and their status.
    Generic utilities for task management, independent of specific task types.
    """

    @staticmethod
    def get_task_status(task_id: str) -> Dict[str, Any]:
        """
        Get the status of a task.

        Args:
            task_id: ID of the task

        Returns:
            Dict with task status information
        """
        result = AsyncResult(task_id, app=celery_app)

        status_info = {
            "task_id": task_id,
            "status": result.status,
            "successful": result.successful() if result.ready() else None,
            "failed": result.failed() if result.ready() else None,
            "ready": result.ready(),
        }

        # Add result or exception if available
        if result.ready():
            if result.successful():
                status_info["result"] = result.get()
            else:
                status_info["error"] = str(result.result)

        return status_info

    @staticmethod
    def cancel_task(task_id: str) -> Dict[str, Any]:
        """
        Cancel a task.

        Args:
            task_id: ID of the task to cancel

        Returns:
            Dict with cancellation status
        """
        result = AsyncResult(task_id, app=celery_app)

        # Check if task can be revoked
        if result.ready():
            return {
                "task_id": task_id,
                "status": "already_completed",
                "message": "Task has already completed and cannot be cancelled"
            }

        # Revoke the task
        result.revoke(terminate=True)

        return {
            "task_id": task_id,
            "status": "cancelled",
            "message": f"Task {task_id} has been cancelled"
        }

    @staticmethod
    async def submit_task(task_func_or_name, *args, **kwargs) -> Dict[str, Any]:
        """
        Generic method to submit a task to Celery.
        Handles both task functions and task names.

        Args:
            task_func_or_name: Either a Celery task function or a string task name
            *args: Positional arguments to pass to the task
            **kwargs: Keyword arguments to pass to the task

        Returns:
            Dict with task_id and status
        """
        # Check if task_func_or_name is a string (task name) or a task function
        if isinstance(task_func_or_name, str):
            # Use send_task for task names
            task = celery_app.send_task(task_func_or_name, args=args, kwargs=kwargs)
            task_name = task_func_or_name
        else:
            # Use delay for task functions
            task = task_func_or_name.delay(*args, **kwargs)
            task_name = task_func_or_name.name

        return {
            "task_id": task.id,
            "status": "submitted",
            "message": f"Task {task_name} submitted for processing"
        }

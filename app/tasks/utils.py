"""
Utility functions for running async code in Celery tasks.
"""
import asyncio
from typing import Any, Callable, TypeVar, Coroutine
from functools import wraps
from app.log import logger

T = TypeVar('T')


def run_async_in_celery(coro: Coroutine[Any, Any, T]) -> T:
    """
    Run an async coroutine in a Celery task context.
    
    This function properly handles the event loop for async operations
    within Celery worker processes.
    
    Args:
        coro: The coroutine to run
        
    Returns:
        The result of the coroutine
    """
    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If there's already a running loop, we need to create a new one
            # This shouldn't happen in normal Celery worker context, but just in case
            logger.warning("Event loop is already running, creating new loop")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(coro)
            loop.close()
            return result
        else:
            # Use the existing loop
            return loop.run_until_complete(coro)
    except RuntimeError:
        # No event loop exists, create a new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(coro)
            return result
        finally:
            loop.close()


def celery_async_task(func: Callable[..., Coroutine[Any, Any, T]]) -> Callable[..., T]:
    """
    Decorator to convert an async function to be usable in Celery tasks.
    
    Usage:
        @celery_async_task
        async def my_async_function(arg1, arg2):
            # async code here
            return result
            
        # In Celery task:
        result = my_async_function(arg1, arg2)  # Will run synchronously
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        coro = func(*args, **kwargs)
        return run_async_in_celery(coro)
    
    return wrapper

from typing import Dict, Any
from celery.exceptions import TimeLimitExceeded, SoftTimeLimitExceeded
from app.tasks import celery_app, run_async_in_celery
from app.log import logger

@celery_app.task(bind=True, max_retries=0, name="tasks.bundle_processor", 
                soft_time_limit=700, time_limit=720)
def bundle_processor(self, bundle_id: int) -> Dict[str, Any]:
    """
    Process a bundle by extracting pages from all files and creating page records.
    This is an idempotent operation.
    """
    from app.controllers.education.bundle_service import BundleService

    try:
        # Run the async processing using the Celery-safe utility
        run_async_in_celery(
            BundleService.process_bundle(self.request.id, bundle_id)
        )

        # Return success result since process_bundle doesn't return anything
        return {
            "status": "success",
            "bundle_id": bundle_id,
            "task_id": self.request.id,
            "message": f"Bundle {bundle_id} processed successfully"
        }

    except SoftTimeLimitExceeded as exc:
        # Handle soft timeout: update bundle status to FAILED
        logger.error(f"Bundle {bundle_id} processing reached soft time limit (task {self.request.id}): {exc}")
        try:
            # Call timeout handler to update bundle status
            run_async_in_celery(
                BundleService.on_timeout_task(self.request.id, bundle_id=bundle_id)
            )
        except Exception as timeout_exc:
            logger.error(f"Failed to handle timeout for bundle {bundle_id}: {timeout_exc}")

        # Re-raise the exception to mark task as failed
        raise exc

    except Exception as exc:
        # Log error and retry
        logger.exception(f"Error processing bundle {bundle_id}: {exc}")
        # raise self.retry(exc=exc, countdown=20)
        raise exec

@celery_app.task(bind=True, max_retries=1, name="tasks.page_processor", 
                soft_time_limit=590, time_limit=600)
def page_processor(self, page_id: int) -> Dict[str, Any]:
    """
    Process a page by performing OCR and object detection.
    This is an idempotent operation.
    """
    from app.controllers.education.page_service import PageService, OcrProcessError, CVProcessError

    try:
        # Run the async processing using the Celery-safe utility
        run_async_in_celery(
            PageService.process_page(self.request.id, page_id)
        )

        # Return success result since process_page doesn't return anything
        return {
            "status": "success",
            "page_id": page_id,
            "task_id": self.request.id,
            "message": f"Page {page_id} processed successfully"
        }

    except SoftTimeLimitExceeded as exc:
        # Handle soft timeout: update page status to FAILED
        logger.error(f"Page {page_id} processing reached soft time limit (task {self.request.id}): {exc}")
        try:
            # Call timeout handler to update page status
            run_async_in_celery(
                PageService.on_timeout_task(self.request.id, page_id=page_id)
            )
        except Exception as timeout_exc:
            logger.error(f"Failed to handle timeout for page {page_id}: {timeout_exc}")

        # Re-raise the exception to mark task as failed
        raise exc

    except OcrProcessError as exc:
        # OCR processing failed - retry this type of error
        logger.exception(f"OCR processing failed for page {page_id}: {exc.message}")
        raise self.retry(exc=exc, countdown=30)

    except CVProcessError as exc:
        # CV processing failed - don't retry, it's likely a permanent issue
        logger.exception(f"CV processing failed for page {page_id}: {exc.message}")
        raise exc

    except Exception as exc:
        # Other errors - don't retry
        logger.exception(f"Error processing page {page_id}: {exc}")
        raise exc

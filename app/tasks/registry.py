"""
任务注册表 - 管理所有可用的任务类型
"""
from typing import Dict, Type, Any
from abc import ABC, abstractmethod


class TaskServiceInterface(ABC):
    """任务服务接口"""

    @staticmethod
    @abstractmethod
    async def start_task(params: Dict[str, Any]) -> Dict[str, Any]:
        """启动任务"""
        pass

    @staticmethod
    @abstractmethod
    def stop_task(task_id: str) -> Dict[str, Any]:
        """停止任务"""
        pass

    @staticmethod
    @abstractmethod
    def status_task(task_id: str) -> Dict[str, Any]:
        """查询任务状态"""
        pass

    @staticmethod
    async def on_timeout_task(task_id: str, **kwargs) -> None:
        """
        处理任务超时的回调方法

        Args:
            task_id: 任务ID
            **kwargs: 额外参数，由具体实现决定
        """
        # 默认实现：记录日志
        from app.log import logger
        _ = kwargs  # Suppress unused parameter warning
        logger.warning(f"Task {task_id} timed out, but no timeout handler implemented")


class TaskRegistry:
    """任务注册表"""

    _services: Dict[str, Type[TaskServiceInterface]] = {}

    @classmethod
    def register(cls, task_name: str, service_class: Type[TaskServiceInterface]):
        """注册任务服务"""
        cls._services[task_name] = service_class

    @classmethod
    def get_service(cls, task_name: str) -> Type[TaskServiceInterface]:
        """获取任务服务"""
        if task_name not in cls._services:
            raise ValueError(f"Unknown task name: {task_name}")
        return cls._services[task_name]

    @classmethod
    def get_available_tasks(cls) -> list:
        """获取所有可用的任务名称"""
        return list(cls._services.keys())


# 任务注册装饰器
def register_task(task_name: str):
    """任务注册装饰器"""
    def decorator(service_class: Type[TaskServiceInterface]):
        TaskRegistry.register(task_name, service_class)
        return service_class
    return decorator

from langgraph.prebuilt import create_react_agent

from app.ai.prompts.template import apply_prompt_template
from app.ai.llms.llm import get_llm_by_type

AGENT_LLM_MAP = {
    "basic": "basic",           # 基础agent使用基础模型
    "reasoning": "reasoning",   # 推理agent使用推理模型
    "vision": "vision",         # 视觉agent使用视觉模型
    "general": "basic",         # 通用agent使用基础模型
    "analyst": "reasoning",     # 分析agent使用推理模型
}

# Create agents using configured LLM types
def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str):
    """Factory function to create agents with consistent configuration."""
    return create_react_agent(
        name=agent_name,
        model=get_llm_by_type(AGENT_LLM_MAP[agent_type]),
        tools=tools,
        prompt=lambda state: apply_prompt_template(prompt_template, state),
    )

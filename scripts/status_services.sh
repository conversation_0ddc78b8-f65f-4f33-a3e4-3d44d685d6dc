#!/bin/bash

# Script to check the status of all services

# Function to show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -v, --verbose              Show detailed information"
    echo ""
    echo "This script checks the status of all services started by start_worker.sh"
}

# Initialize variables
VERBOSE=false

# Process command line options
while [[ $# -gt 0 ]]; do
    case "$1" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
    shift
done

# Function to check service status
check_service_status() {
    local service_name=$1
    local pid_file=$2
    local port=$3
    
    echo -n "[$service_name] "
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo -n "RUNNING (PID: $pid)"
            
            # Check if port is listening (if port is provided)
            if [ -n "$port" ]; then
                if lsof -i:$port > /dev/null 2>&1; then
                    echo " - Port $port: LISTENING"
                else
                    echo " - Port $port: NOT LISTENING"
                fi
            else
                echo ""
            fi
            
            if [ "$VERBOSE" = "true" ]; then
                echo "    Command: $(ps -p $pid -o cmd --no-headers 2>/dev/null || echo 'N/A')"
                echo "    Started: $(ps -p $pid -o lstart --no-headers 2>/dev/null || echo 'N/A')"
            fi
        else
            echo "STOPPED (stale PID file)"
            if [ "$VERBOSE" = "true" ]; then
                echo "    PID file exists but process is not running"
            fi
        fi
    else
        echo "STOPPED (no PID file)"
        
        # Check if something else is using the port
        if [ -n "$port" ] && lsof -i:$port > /dev/null 2>&1; then
            echo "    WARNING: Port $port is in use by another process"
            if [ "$VERBOSE" = "true" ]; then
                echo "    Process using port: $(lsof -i:$port -t | head -1 | xargs ps -p | tail -1)"
            fi
        fi
    fi
}

echo "Service Status Report"
echo "===================="

# Create pids directory if it doesn't exist
mkdir -p pids

# Check each service
check_service_status "FastAPI" "pids/api.pid" "9999"
check_service_status "Celery Worker" "pids/worker.pid" ""
check_service_status "Flower" "pids/flower.pid" "5555"
check_service_status "Frontend" "pids/frontend.pid" "3100"

echo ""
echo "Log Files:"
echo "=========="
for log_file in logs/*.log; do
    if [ -f "$log_file" ]; then
        echo "  $log_file ($(wc -l < "$log_file") lines)"
        if [ "$VERBOSE" = "true" ]; then
            echo "    Last modified: $(stat -c %y "$log_file" 2>/dev/null || stat -f %Sm "$log_file" 2>/dev/null || echo 'N/A')"
            echo "    Size: $(du -h "$log_file" | cut -f1)"
        fi
    fi
done

if [ "$VERBOSE" = "true" ]; then
    echo ""
    echo "System Information:"
    echo "=================="
    echo "  Load average: $(uptime | awk -F'load average:' '{print $2}')"
    echo "  Memory usage: $(free -h 2>/dev/null | grep Mem || echo 'N/A')"
    echo "  Disk usage: $(df -h . | tail -1)"
fi

#!/bin/bash

# Script to stop all services started by start_worker.sh

# Function to show usage
show_usage() {
    echo "Usage: $0 [options] [services...]"
    echo ""
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -f, --force                Force kill processes (SIGKILL instead of SIGTERM)"
    echo ""
    echo "Services:"
    echo "  all                        Stop all services (default if no service specified)"
    echo "  api                        Stop FastAPI service"
    echo "  worker                     Stop Celery worker"
    echo "  flower                     Stop Flower monitoring service"
    echo "  frontend                   Stop Vue frontend development server"
    echo ""
    echo "Examples:"
    echo "  $0 all                     # Stop all services"
    echo "  $0 api worker              # Stop API and worker only"
    echo "  $0 -f frontend             # Force stop frontend"
}

# Initialize variables
FORCE_KILL=false
SERVICES=""

# Process command line options
while [[ $# -gt 0 ]]; do
    case "$1" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -f|--force)
            FORCE_KILL=true
            ;;
        all|api|worker|flower|frontend)
            SERVICES="${SERVICES} $1"
            ;;
        *)
            echo "Unknown option or service: $1"
            show_usage
            exit 1
            ;;
    esac
    shift
done

# If no service specified, stop all
if [ -z "$SERVICES" ]; then
    SERVICES="all"
fi

# Function to stop a service by PID file
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Stopping $service_name (PID: $pid)..."
            if [ "$FORCE_KILL" = "true" ]; then
                kill -9 "$pid" 2>/dev/null
            else
                kill -TERM "$pid" 2>/dev/null
                # Wait up to 10 seconds for graceful shutdown
                for i in {1..10}; do
                    if ! kill -0 "$pid" 2>/dev/null; then
                        break
                    fi
                    sleep 1
                done
                # Force kill if still running
                if kill -0 "$pid" 2>/dev/null; then
                    echo "  Force killing $service_name..."
                    kill -9 "$pid" 2>/dev/null
                fi
            fi
            echo "  $service_name stopped."
        else
            echo "$service_name is not running (stale PID file)."
        fi
        rm -f "$pid_file"
    else
        echo "$service_name PID file not found. Service may not be running."
    fi
}

# Function to stop API
stop_api() {
    stop_service "FastAPI" "pids/api.pid"
}

# Function to stop worker
stop_worker() {
    stop_service "Celery worker" "pids/worker.pid"
}

# Function to stop flower
stop_flower() {
    stop_service "Flower" "pids/flower.pid"
}

# Function to stop frontend
stop_frontend() {
    stop_service "Frontend" "pids/frontend.pid"
}

# Create pids directory if it doesn't exist
mkdir -p pids

# Stop services based on command line arguments
if [[ "$SERVICES" == *"all"* ]]; then
    # Stop all services in reverse order
    echo "Stopping all services..."
    stop_frontend
    stop_flower
    stop_worker
    stop_api
else
    # Stop specific services
    for SERVICE in $SERVICES; do
        case $SERVICE in
            api)
                stop_api
                ;;
            worker)
                stop_worker
                ;;
            flower)
                stop_flower
                ;;
            frontend)
                stop_frontend
                ;;
        esac
    done
fi

echo "Stop operation completed."

# Clean up any remaining processes by name (fallback)
if [[ "$SERVICES" == *"all"* ]] || [[ "$FORCE_KILL" = "true" ]]; then
    echo "Checking for any remaining processes..."
    
    # Kill any remaining uv processes related to our services
    pkill -f "uv run run.py" 2>/dev/null && echo "  Killed remaining FastAPI processes"
    pkill -f "uv run -m celery.*worker" 2>/dev/null && echo "  Killed remaining Celery worker processes"
    pkill -f "uv run -m celery.*flower" 2>/dev/null && echo "  Killed remaining Flower processes"
    
    # Kill any remaining frontend processes
    pkill -f "pnpm dev" 2>/dev/null && echo "  Killed remaining pnpm dev processes"
    pkill -f "npm run dev" 2>/dev/null && echo "  Killed remaining npm dev processes"
fi

echo "All services stopped."

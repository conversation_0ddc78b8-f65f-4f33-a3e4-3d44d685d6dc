# Worker Scripts

This directory contains scripts for managing Celery workers and related services.

## start_worker.sh

The `start_worker.sh` script is a utility script for starting Celery workers with configurable parameters. It provides a convenient way to start workers with specific configurations in different environments.

### Relationship with docker-compose.yaml

The `start_worker.sh` script and the `celery-worker` service in `docker-compose.yaml` serve similar purposes but are used in different contexts:

1. **docker-compose.yaml's celery-worker service**:
   - Used for containerized deployment in Docker environments
   - Automatically started when running `docker-compose up`
   - Configuration is set through environment variables in the docker-compose.yaml file
   - Ideal for development and production environments using Docker

2. **scripts/start_worker.sh**:
   - Used for manual worker management, especially in non-Docker environments
   - Can be run directly on the host machine
   - Provides more flexibility for custom configurations
   - Useful for:
     - Local development without Docker
     - Running additional workers with different configurations
     - Testing specific worker configurations
     - Production environments that don't use Docker

### Usage Scenarios

#### Docker Environment

In a Docker environment, the worker is started automatically as part of the `docker-compose up` command:

```bash
docker-compose up
```

This will start all services defined in docker-compose.yaml, including the celery-worker service.

#### Non-Docker Environment

In a non-Docker environment, you can use the start_worker.sh script to start services:

```bash
# Navigate to the project root
cd /path/to/project

# Make sure the script is executable
chmod +x scripts/start_worker.sh

# Run the script with default settings (interactive mode)
./scripts/start_worker.sh

# Run in daemon mode (detached from terminal)
./scripts/start_worker.sh -d all

# Start specific services only
./scripts/start_worker.sh api worker

# Or with custom environment variables
CELERY_CONCURRENCY=8 CELERY_LOGLEVEL=debug ./scripts/start_worker.sh -d all
```

#### Service Management

The script suite provides comprehensive service management:

```bash
# Start all services in daemon mode
./scripts/start_worker.sh -d all

# Check service status
./scripts/status_services.sh

# Check detailed status
./scripts/status_services.sh -v

# Stop all services
./scripts/stop_services.sh

# Force stop all services
./scripts/stop_services.sh -f

# Stop specific services
./scripts/stop_services.sh api worker
```

### Configuration

Both methods support configuration through environment variables:

| Environment Variable | Description | Default Value |
|---------------------|-------------|---------------|
| CELERY_CONCURRENCY | Number of worker processes | 4 |
| CELERY_LOGLEVEL | Log level (debug, info, warning, error) | info |
| CELERY_QUEUE | Queue to consume from | celery |
| CELERY_MAX_TASKS_PER_CHILD | Maximum number of tasks a worker process can execute before it's replaced | 200 |
| CELERY_WORKER_NAME | Name of the worker | worker1 |

### When to Use Which

- **Use docker-compose.yaml** when:
  - You're running the entire application stack with Docker
  - You want a consistent, reproducible environment
  - You're deploying to production using Docker

- **Use start_worker.sh** when:
  - You're running the application without Docker
  - You need to start additional workers with different configurations
  - You're debugging worker-specific issues
  - You need more control over worker parameters

#!/bin/bash

# Main startup script for all services
# This script can start <PERSON><PERSON><PERSON>, Celery worker, Flower monitoring service, and Frontend

# Set daemon mode flag
DAEMON_MODE=${DAEMON_MODE:-false}

# Set default values for worker
CONCURRENCY=${CELERY_CONCURRENCY:-4}
LOGLEVEL=${CELERY_LOGLEVEL:-info}
QUEUE=${CELERY_QUEUE:-celery}
MAX_TASKS_PER_CHILD=${CELERY_MAX_TASKS_PER_CHILD:-200}
WORKER_NAME=${CELERY_WORKER_NAME:-worker1}

# Set default values for Flower
FLOWER_PORT=${FLOWER_PORT:-5555}
FLOWER_HOST=${FLOWER_HOST:-0.0.0.0}
FLOWER_URL_PREFIX=${FLOWER_URL_PREFIX:-""}
FLOWER_BASIC_AUTH=${FLOWER_BASIC_AUTH:-""}

# Set default values for FastAPI
API_PORT=${API_PORT:-9999}
API_HOST=${API_HOST:-0.0.0.0}

# Set default values for Frontend
FRONTEND_PORT=${FRONTEND_PORT:-3100}
FRONTEND_HOST=${FRONTEND_HOST:-0.0.0.0}

# Create logs and pids directories if they don't exist
mkdir -p logs pids

# Function to show usage
show_usage() {
    echo "Usage: $0 [options] [services...]"
    echo ""
    echo "Options:"
    echo "  -h, --help                 Show this help message"
    echo "  -d, --daemon               Run in daemon mode (detach from terminal)"
    echo ""
    echo "Services:"
    echo "  all                        Start all services (default if no service specified)"
    echo "  api                        Start FastAPI service"
    echo "  worker                     Start Celery worker"
    echo "  flower                     Start Flower monitoring service"
    echo "  frontend                   Start Vue frontend development server"
    echo ""
    echo "Examples:"
    echo "  $0 all                     # Start all services"
    echo "  $0 -d all                  # Start all services in daemon mode"
    echo "  $0 api worker              # Start API and worker only"
    echo "  $0 worker                  # Start worker only"
    echo "  $0 frontend                # Start frontend only"
    echo ""
    echo "Environment variables:"
    echo "  API_PORT                   Port for FastAPI (default: 9999)"
    echo "  API_HOST                   Host for FastAPI (default: 0.0.0.0)"
    echo "  CELERY_CONCURRENCY         Number of worker processes (default: 4)"
    echo "  CELERY_LOGLEVEL            Log level (default: info)"
    echo "  CELERY_QUEUE               Queue name (default: celery)"
    echo "  CELERY_MAX_TASKS_PER_CHILD Max tasks per child (default: 200)"
    echo "  CELERY_WORKER_NAME         Worker name (default: worker1)"
    echo "  FLOWER_PORT                Port for Flower (default: 5555)"
    echo "  FLOWER_HOST                Host for Flower (default: 0.0.0.0)"
    echo "  FLOWER_URL_PREFIX          URL prefix for Flower (default: \"\")"
    echo "  FLOWER_BASIC_AUTH          Basic auth for Flower (default: \"\")"
    echo "  FRONTEND_PORT              Port for frontend dev server (default: 3100)"
    echo "  FRONTEND_HOST              Host for frontend dev server (default: 0.0.0.0)"
    echo "  DAEMON_MODE                Run in daemon mode (default: false)"
}

# Process command line options
while [[ $# -gt 0 ]]; do
    case "$1" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -d|--daemon)
            DAEMON_MODE=true
            ;;
        all|api|worker|flower|frontend)
            SERVICES="${SERVICES} $1"
            ;;
        *)
            echo "Unknown option or service: $1"
            show_usage
            exit 1
            ;;
    esac
    shift
done

# If no service specified, start all
if [ -z "$SERVICES" ]; then
    SERVICES="all"
fi

# If daemon mode is enabled, restart the script in background with setsid
if [ "$DAEMON_MODE" = "true" ] && [ -z "$_DAEMON_STARTED" ]; then
    echo "Starting in daemon mode..."
    export _DAEMON_STARTED=1
    export DAEMON_MODE=false  # Prevent infinite recursion
    setsid "$0" $SERVICES </dev/null >/dev/null 2>&1 &
    echo "Services started in daemon mode. Check logs/ directory for output."
    echo "Use 'scripts/stop_services.sh' to stop all services."
    exit 0
fi

# Array to store PIDs
declare -a SERVICE_PIDS

# Function to check if a service is already running
check_service_running() {
    local service_name=$1
    local port=$2

    if lsof -i:$port > /dev/null 2>&1; then
        echo "WARNING: Port $port is already in use. $service_name might already be running."
        return 0
    fi
    return 1
}

# Function to start FastAPI
start_api() {
    if check_service_running "FastAPI" $API_PORT; then
        return
    fi

    echo "Starting FastAPI service with the following configuration:"
    echo "  - Host: $API_HOST"
    echo "  - Port: $API_PORT"

    nohup uv run run.py --host $API_HOST --port $API_PORT > logs/api.log 2>&1 &
    API_PID=$!
    echo $API_PID > pids/api.pid
    echo "FastAPI service started with PID: $API_PID"
    SERVICE_PIDS+=($API_PID)

    # Wait for API to be ready
    echo "Waiting for API to be ready..."
    for i in {1..30}; do
        if curl -s "http://$API_HOST:$API_PORT/api/v1/health" > /dev/null 2>&1; then
            echo "API is ready!"
            break
        fi
        if [ $i -eq 30 ]; then
            echo "WARNING: API did not respond within the expected time."
        fi
        sleep 1
    done
}

# Function to start Celery worker
start_worker() {
    echo "Starting Celery worker with the following configuration:"
    echo "  - Concurrency: $CONCURRENCY"
    echo "  - Log level: $LOGLEVEL"
    echo "  - Queue: $QUEUE"
    echo "  - Max tasks per child: $MAX_TASKS_PER_CHILD"
    echo "  - Worker name: $WORKER_NAME"

    nohup uv run -m celery -A app.tasks.celery_app worker \
        --concurrency=$CONCURRENCY \
        --loglevel=$LOGLEVEL \
        --queues=$QUEUE \
        --max-tasks-per-child=$MAX_TASKS_PER_CHILD \
        --hostname=$WORKER_NAME@%h \
        --logfile=logs/celery-$WORKER_NAME.log > logs/celery-worker.log 2>&1 &

    WORKER_PID=$!
    echo $WORKER_PID > pids/worker.pid
    echo "Celery worker started with PID: $WORKER_PID"
    SERVICE_PIDS+=($WORKER_PID)

    # Give worker a moment to initialize
    sleep 2
}

# Function to start Flower
start_flower() {
    if check_service_running "Flower" $FLOWER_PORT; then
        return
    fi

    echo "Starting Flower monitoring service with the following configuration:"
    echo "  - Host: $FLOWER_HOST"
    echo "  - Port: $FLOWER_PORT"
    echo "  - URL Prefix: $FLOWER_URL_PREFIX"
    echo "  - Basic Auth: ${FLOWER_BASIC_AUTH:+Enabled}"

    FLOWER_ARGS=""
    if [ -n "$FLOWER_URL_PREFIX" ]; then
        FLOWER_ARGS="$FLOWER_ARGS --url-prefix=$FLOWER_URL_PREFIX"
    fi

    if [ -n "$FLOWER_BASIC_AUTH" ]; then
        FLOWER_ARGS="$FLOWER_ARGS --basic-auth=$FLOWER_BASIC_AUTH"
    fi

    echo "Starting Flower with arguments: $FLOWER_ARGS"
    nohup uv run -m celery -A app.tasks.celery_app flower \
        --address=$FLOWER_HOST \
        --port=$FLOWER_PORT \
        --log-file-prefix=logs/flower.log \
        $FLOWER_ARGS > logs/flower-service.log 2>&1 &

    FLOWER_PID=$!
    echo $FLOWER_PID > pids/flower.pid
    echo "Flower monitoring service started with PID: $FLOWER_PID"
    SERVICE_PIDS+=($FLOWER_PID)
}

# Function to start Frontend
start_frontend() {
    if check_service_running "Frontend" $FRONTEND_PORT; then
        return
    fi

    echo "Starting Frontend development server with the following configuration:"
    echo "  - Host: $FRONTEND_HOST"
    echo "  - Port: $FRONTEND_PORT"

    # Check if we're in the project root
    if [ -d "web" ]; then
        cd web
        # Set environment variables for frontend
        export VITE_PORT=$FRONTEND_PORT
        export VITE_HOST=$FRONTEND_HOST

        # Start the frontend dev server
        echo "Starting frontend development server..."
        if command -v pnpm &> /dev/null; then
            nohup setsid pnpm dev > ../logs/frontend.log 2>&1 &
        else
            nohup setsid npm run dev > ../logs/frontend.log 2>&1 &
        fi

        FRONTEND_PID=$!
        echo $FRONTEND_PID > ../pids/frontend.pid
        echo "Frontend development server started with PID: $FRONTEND_PID"
        SERVICE_PIDS+=($FRONTEND_PID)
        cd ..

        # Wait for frontend to be ready
        echo "Waiting for frontend to be ready..."
        for i in {1..60}; do
            if curl -s "http://$FRONTEND_HOST:$FRONTEND_PORT" > /dev/null 2>&1; then
                echo "Frontend is ready! Available at http://$FRONTEND_HOST:$FRONTEND_PORT"
                break
            fi
            if [ $i -eq 60 ]; then
                echo "WARNING: Frontend did not respond within the expected time."
            fi
            sleep 1
        done
    else
        echo "ERROR: 'web' directory not found. Make sure you're running this script from the project root."
        return 1
    fi
}

# Start services based on command line arguments with proper order
if [[ "$SERVICES" == *"all"* ]]; then
    # Start all services in the correct order
    start_api
    start_worker
    start_flower
    start_frontend
else
    # Start services in the specified order, respecting dependencies
    for SERVICE in $SERVICES; do
        case $SERVICE in
            api)
                start_api
                ;;
            worker)
                # Worker depends on API
                if [[ "$SERVICES" != *"api"* ]]; then
                    echo "Note: Starting worker without API. Make sure API is running separately."
                fi
                start_worker
                ;;
            flower)
                # Flower depends on worker
                if [[ "$SERVICES" != *"worker"* ]]; then
                    echo "Note: Starting Flower without worker. Make sure worker is running separately."
                fi
                start_flower
                ;;
            frontend)
                # Frontend depends on API
                if [[ "$SERVICES" != *"api"* ]]; then
                    echo "Note: Starting frontend without API. Make sure API is running separately."
                fi
                start_frontend
                ;;
        esac
    done
fi

echo "All requested services started. Press Ctrl+C to stop."

# Handle termination
trap 'echo "Stopping all services..."; for PID in "${SERVICE_PIDS[@]}"; do kill $PID 2>/dev/null; done; echo "All services stopped."; exit 0' SIGINT SIGTERM

# Wait for all background processes
wait

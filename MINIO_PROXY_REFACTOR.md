# MinIO代理重构说明

## 重构目标

解决MinIO内网URL无法外部访问的问题，通过nginx反向代理实现MinIO文件的外部访问，同时保持签名验证的安全性。

## 重构方案

### 数据流程

```
用户浏览器 -> nginx (web.conf) -> 前端页面 -> FastAPI后端 -> MinIO内部服务
     ↑                                                              ↓
     └─────────── nginx代理 /minio/* ←─────── 相对路径URL ←─────────┘
```

### 核心改动

#### 1. 后端操作 (`app/utils/minio_client.py`)

**修改前:**
```python
def get_presigned_url(self, bucket: str, fnm: str, expires: Union[int, timedelta]) -> str:
    return self.conn.get_presigned_url("GET", bucket, fnm, expires_delta)
    # 返回: http://minio:9000/bucket/file?signature=...
```

**修改后:**
```python
def get_presigned_url(self, bucket: str, fnm: str, expires: Union[int, timedelta]) -> str:
    original_url = self.conn.get_presigned_url("GET", bucket, fnm, expires_delta)
    parsed_url = urlparse(original_url)
    relative_path = f"/minio{parsed_url.path}"
    if parsed_url.query:
        relative_path += f"?{parsed_url.query}"
    return relative_path
    # 返回: /minio/bucket/file?signature=...
```

#### 2. Nginx配置 (`deploy/web.conf`)

**关键配置:**
```nginx
location /minio/ {
    # 去掉/minio前缀，直接转发到MinIO服务器
    proxy_pass http://minio:9000/;
    
    # 关键：设置正确的Host头部，确保签名验证通过
    proxy_set_header Host minio:9000;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 性能优化
    proxy_buffering off;
    proxy_request_buffering off;
    
    # 跨域支持
    add_header 'Access-Control-Allow-Origin' '*' always;
    # ... 其他CORS配置
}
```

#### 3. 前端操作

**修改的组件:**
- `GeneralPagePreviewer.vue`
- `GeneralPageEditor.vue`
- `PagePreviewer.vue`
- `PageEditor.vue`
- `EducationPagePreviewer.vue`
- `EducationPageEditor.vue`

**URL验证逻辑更新:**
```javascript
// 修改前
if (url && (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('/api/'))) {
    imageUrl.value = url
}

// 修改后
if (url && (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('/api/') || url.startsWith('/minio/'))) {
    imageUrl.value = url
}
```

## 工作原理

### 1. URL生成过程

1. **后端生成内部URL**: MinIO SDK生成完整的内部预签名URL
   ```
   http://minio:9000/files/image.jpg?X-Amz-Algorithm=...&X-Amz-Signature=...
   ```

2. **后端转换为相对路径**: 提取路径和查询参数，添加`/minio`前缀
   ```
   /minio/files/image.jpg?X-Amz-Algorithm=...&X-Amz-Signature=...
   ```

3. **前端使用相对路径**: 浏览器自动拼接当前域名
   ```
   https://yourdomain.com/minio/files/image.jpg?X-Amz-Algorithm=...&X-Amz-Signature=...
   ```

### 2. 请求处理过程

1. **浏览器发起请求**: 向`https://yourdomain.com/minio/files/image.jpg?signature=...`发起请求

2. **Nginx接收请求**: 匹配`location /minio/`规则

3. **Nginx转发请求**: 
   - 去掉URL中的`/minio`前缀
   - 设置Host头部为`minio:9000`
   - 转发到`http://minio:9000/files/image.jpg?signature=...`

4. **MinIO验证签名**: 根据Host头部和签名参数验证请求合法性

5. **返回文件内容**: MinIO返回文件，Nginx转发给浏览器

## 优势

1. **安全性**: 不暴露MinIO端口到公网
2. **兼容性**: 适应不同域名部署
3. **性能**: 利用nginx的高性能代理能力
4. **维护性**: 集中的代理配置管理

## 测试验证

运行测试脚本验证重构结果:
```bash
python test_minio_proxy.py
```

测试内容包括:
- MinIO连接测试
- 预签名URL生成测试
- URL格式验证
- Nginx配置检查

## 部署注意事项

1. **重启服务**: 重构后需要重启应用和nginx
2. **域名配置**: 确保nginx配置中的域名设置正确
3. **网络连通性**: 确保nginx能访问MinIO内部服务
4. **签名时间**: 注意预签名URL的过期时间设置

## 故障排查

### 常见问题

1. **图片无法显示**
   - 检查nginx配置是否正确
   - 检查MinIO服务是否正常运行
   - 查看浏览器开发者工具的网络请求

2. **签名验证失败**
   - 确认nginx设置了正确的Host头部
   - 检查时间同步问题

3. **跨域问题**
   - 确认nginx配置了正确的CORS头部

### 调试方法

1. **查看nginx日志**:
   ```bash
   docker logs vue-fastapi-admin
   ```

2. **查看MinIO日志**:
   ```bash
   docker logs vue-fastapi-minio
   ```

3. **测试直接访问**:
   ```bash
   curl -I "https://yourdomain.com/minio/bucket/file?signature=..."
   ```

## 回滚方案

如果重构出现问题，可以通过以下步骤回滚:

1. 恢复`app/utils/minio_client.py`中的`get_presigned_url`方法
2. 恢复前端组件中的URL验证逻辑
3. 重启应用

注意: 回滚后需要确保MinIO端口可以外部访问，或者使用其他解决方案。

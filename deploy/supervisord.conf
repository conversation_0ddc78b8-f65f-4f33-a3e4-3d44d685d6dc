[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisor/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
user=appuser
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log
priority=10

[program:fastapi]
command=python run.py
directory=/app
user=appuser
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/fastapi.err.log
stdout_logfile=/var/log/supervisor/fastapi.out.log
priority=20

[program:celery-worker]
command=celery -A app.tasks.celery_app worker --concurrency=2 --loglevel=info
directory=/app
user=appuser
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/celery-worker.err.log
stdout_logfile=/var/log/supervisor/celery-worker.out.log
priority=30

[program:flower]
command=celery -A app.tasks.celery_app flower --port=5555
directory=/app
user=appuser
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/flower.err.log
stdout_logfile=/var/log/supervisor/flower.out.log
priority=40 
# This file was autogenerated by uv via the following command:
#    uv export --format requirements-txt --output-file requirements.txt --no-hashes
aerich==0.8.1
aiohappyeyeballs==2.6.1
aiohttp==3.8.6
aiosignal==1.3.2
aiosqlite==0.20.0
amqp==5.3.1
annotated-types==0.7.0
anyio==4.8.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
async-timeout==5.0.1 ; python_full_version < '3.11.3'
asyncclick==8.1.8
attrs==25.3.0
billiard==4.2.1
black==24.10.0
celery==5.5.2
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6 ; sys_platform == 'win32'
dataclasses-json==0.6.7
dictdiffer==0.9.0
distro==1.9.0
dnspython==2.7.0
email-validator==2.2.0
fastapi==0.111.0
fastapi-cli==0.0.7
flower==2.0.1
frozenlist==1.6.0
greenlet==3.2.2 ; (python_full_version < '3.14' and platform_machine == 'AMD64') or (python_full_version < '3.14' and platform_machine == 'WIN32') or (python_full_version < '3.14' and platform_machine == 'aarch64') or (python_full_version < '3.14' and platform_machine == 'amd64') or (python_full_version < '3.14' and platform_machine == 'ppc64le') or (python_full_version < '3.14' and platform_machine == 'win32') or (python_full_version < '3.14' and platform_machine == 'x86_64')
h11==0.14.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
humanize==4.12.3
idna==3.10
iso8601==2.1.0
isort==5.13.2
jinja2==3.1.5
jiter==0.10.0
jsonpatch==1.33
jsonpointer==3.0.0
kombu==5.5.3
langchain==0.3.25
langchain-community==0.3.24
langchain-core==0.3.60
langchain-experimental==0.3.4
langchain-openai==0.3.17
langchain-text-splitters==0.3.8
langgraph==0.4.5
langgraph-checkpoint==2.0.26
langgraph-prebuilt==0.1.8 ; python_full_version < '4.0'
langgraph-sdk==0.1.70 ; python_full_version < '4.0'
langsmith==0.3.42
loguru==0.7.3
markdown-it-py==3.0.0
markupsafe==3.0.2
marshmallow==3.26.1
mdurl==0.1.2
minio==7.2.15
multidict==6.4.4
mypy-extensions==1.0.0
numpy==2.2.6
openai==1.81.0
opencv-python==*********
orjson==3.10.14
ormsgpack==1.9.1
packaging==24.2
passlib==1.7.4
pathspec==0.12.1
platformdirs==4.3.6
prometheus-client==0.22.0
prompt-toolkit==3.0.51
propcache==0.3.1
pycparser==2.22
pycryptodome==3.22.0
pydantic==2.10.5
pydantic-core==2.27.2
pydantic-settings==2.7.1
pygments==2.19.1
pyjwt==2.10.1
pymupdf==1.25.5
pypika-tortoise==0.3.2
pyproject-toml==0.1.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-multipart==0.0.20
pytz==2024.2
pyyaml==6.0.2
redis==6.1.0
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rich-toolkit==0.13.2
ruff==0.9.1
setuptools==75.8.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
sqlalchemy==2.0.41
starlette==0.37.2
tenacity==9.1.2
tiktoken==0.9.0
tornado==6.5
tortoise-orm==0.23.0
tqdm==4.67.1
typer==0.15.1
typing-extensions==4.12.2
typing-inspect==0.9.0
tzdata==2025.2
ujson==5.10.0
urllib3==2.4.0
uvicorn==0.34.0
uvloop==0.21.0
vine==5.1.0
watchfiles==1.0.4
wcwidth==0.2.13
websockets==14.1
wheel==0.45.1
win32-setctime==1.2.0 ; sys_platform == 'win32'
xxhash==3.5.0
yarl==1.20.0
zstandard==0.23.0

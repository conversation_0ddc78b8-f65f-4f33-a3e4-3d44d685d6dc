# 数据和缓存目录
volumes/
logs/
__pycache__/
.pytest_cache/
.ruff_cache/
.mypy_cache/
.vite/
tmp/
temp/
.tmp/

# 开发环境
venv/
.venv/
env/
ENV/
.idea/
.vscode/
*.swp
*.swo
*~

# 数据库文件
*.sqlite3
*.sqlite3-journal
*.sqlite3-shm
*.sqlite3-wal
*.db

# 前端构建缓存和依赖
web/node_modules/
web/dist/
web/.vite/
web/stats.html
web/package-lock.json
web/yarn.lock
# Note: Keep pnpm-lock.yaml for Docker build

# Python 相关
*.py[cod]
*$py.class
*.so
build/
dist/
*.egg-info/
.installed.cfg
*.egg
.coverage
.tox/
.cache
coverage.xml
*.cover
.hypothesis/
.dmypy.json
dmypy.json

# 系统文件
.DS_Store
._.DS_Store
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git相关
.git/
.github/
.gitignore
.gitattributes

# 文档和非必要文件
*.md
deploy/sample-picture/
rules/
scripts/
Makefile

# 日志和进程文件
*.log
nohup.out
pids/
*.pid
*.seed
*.pid.lock

# 配置文件
.env
.env.local
.env.*.local
# Note: Keep uv.lock for Docker build

# Docker 相关
Dockerfile*
docker-compose*.yml
docker-compose*.yaml
.dockerignore

# CI/CD
.travis.yml
.circleci/
.gitlab-ci.yml

# 测试文件
test/
tests/
*_test.py
test_*.py

# 备份文件
*.bak
*.backup
*.old

# 其他开发文件
data/

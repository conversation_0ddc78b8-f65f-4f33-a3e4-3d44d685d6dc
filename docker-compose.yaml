services:
  # Main Application Service
  app:
    container_name: vue-fastapi-admin
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: vue-fastapi-admin:latest
    ports:
      - "80:80"
      - "5555:5555"  # Flower port
      - "9999:9999"
    depends_on:
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - fastapi-file
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ${SQLITE_DATA_PATH:-./data/sqlite}:/app/data/sqlite:rw
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MinIO Object Storage Service
  minio:
    container_name: vue-fastapi-minio
    image: quay.io/minio/minio:RELEASE.2025-04-22T22-12-26Z
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin}
    volumes:
      - ${MINIO_DATA_PATH:-./data/minio}:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    ports:
      - "9000:9000"  # API port
      - "9001:9001"  # Console port
    networks:
      - fastapi-file
    restart: unless-stopped

  # Redis Service
  redis:
    container_name: vue-fastapi-redis
    image: redis:8.0.1-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redispassword}
    volumes:
      - ${REDIS_DATA_PATH:-./data/redis}:/data
    ports:
      - "6379:6379"
    networks:
      - fastapi-file
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redispassword}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  fastapi-file:
    driver: bridge



# Todo List

## 待解决问题

### 1. datetime 序列化问题

**问题描述**：
当前项目中的 datetime 对象在 JSON 序列化时会导致 `TypeError: Object of type datetime is not JSON serializable` 错误。我们目前采用了在多个地方添加自定义序列化逻辑的方式解决，但这种方法导致代码冗余且难以维护。

**当前解决方案**：
- 在 FileBase 和 FileListResponse 模型中添加自定义 model_dump 方法
- 在 JSONResponse 子类中重写 render 方法
- 添加自定义 DateTimeEncoder 类
- 在每个 API 端点中手动处理 datetime 序列化

**问题影响**：
- 代码冗余
- 维护困难
- 可能在未来添加新模型时忘记处理 datetime 序列化

**可能的优化方案**：

1. **使用 FastAPI 全局 JSON 编码器**：
   ```python
   # app/main.py 或 app/__init__.py
   from fastapi.encoders import jsonable_encoder
   from datetime import datetime
   import json

   class DateTimeEncoder(json.JSONEncoder):
       def default(self, obj):
           if isinstance(obj, datetime):
               return obj.isoformat()
           return super().default(obj)

   app = FastAPI(
       # 其他配置...
       json_encoder=DateTimeEncoder
   )
   ```

2. **在 Pydantic 基类中配置 json_encoders**：
   ```python
   # app/schemas/base.py
   from datetime import datetime
   from pydantic import BaseModel

   class BaseSchema(BaseModel):
       class Config:
           json_encoders = {
               datetime: lambda dt: dt.isoformat()
           }
   ```
   然后让所有其他 schema 继承这个基类。

3. **使用 FastAPI 的响应模型**：
   确保所有 API 端点都使用 response_model 参数，让 FastAPI 自动处理序列化：
   ```python
   @app.get("/items/", response_model=List[Item])
   async def read_items():
       return items  # 包含 datetime 字段的对象列表
   ```

4. **使用第三方库**：
   考虑使用专门处理 JSON 序列化的库，如 `orjson`：
   ```python
   # app/main.py
   from fastapi.responses import ORJSONResponse

   app = FastAPI(
       default_response_class=ORJSONResponse
   )
   ```

**推荐方案**：
结合方案 1 和方案 2，在应用程序级别配置 JSON 编码器，并在基础 Pydantic 模型中设置 json_encoders。这样可以确保在所有地方一致地处理 datetime 序列化，同时最小化代码修改。

**实施计划**：
1. 创建基础 Pydantic 模型类，配置 json_encoders
2. 修改现有模型继承该基类
3. 在 FastAPI 应用程序初始化时配置全局 JSON 编码器
4. 移除当前的自定义序列化逻辑
5. 测试所有 API 端点确保正常工作

**优先级**：中
**预计工作量**：2-3小时

### 2. FileUpdate schema 为什么定义是 optional None, 但在swagger ui上默认值是0

### 3. 文件上传时出现 422 Unprocessable Entity 错误 (Done)

**问题描述**：
在文件上传过程中，API 请求返回 "POST /api/v1/file/upload HTTP/1.1" 422 Unprocessable Entity 错误。问题出在中间件处理文件上传请求的方式上，导致请求体被消费后无法被路由处理函数再次读取。

**原因分析**：
1. `HttpAuditLogMiddleware` 中间件在 `get_request_args` 方法中尝试解析所有 POST/PUT/PATCH 请求的请求体
2. 对于 multipart/form-data 请求（文件上传），中间件调用 `await request.form()` 消费了请求流
3. 一旦请求流被消费，路由处理函数无法再次读取，导致 422 错误

**解决方案**：
1. 修改 `HttpAuditLogMiddleware` 中的 `get_request_args` 方法，对于 multipart/form-data 请求，不尝试解析请求体
2. 只记录文件上传的基本信息（content-type, content-length），而不消费请求流
3. 添加更详细的日志记录，以便于调试类似问题

**实施细节**：
```python
# 修改 get_request_args 方法
if "multipart/form-data" in content_type:
    args["_file_upload"] = {
        "content_type": content_type,
        "content_length": request.headers.get("content-length", "unknown")
    }
    logger.info(f"File upload detected, skipping body parsing in middleware")
```

**经验教训**：
1. FastAPI 中间件在处理请求体时需要特别小心，一旦请求流被消费就无法再次读取
2. 对于文件上传等特殊请求，中间件应该避免消费请求流
3. 添加详细的日志记录有助于诊断和解决类似问题
